﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FlowCheck_DPLNG
{
    public static class SqlCommandExtensions
    {
        /// <summary>
        /// 生成可用于调试和日志记录的SQL语句字符串。
        /// 注意：这只是一个模拟，不是服务器上真正执行的命令文本。
        /// </summary>
        public static string GetGeneratedSql(this SqlCommand command)
        {
            var sb = new StringBuilder(command.CommandText);

            foreach (SqlParameter param in command.Parameters)
            {
                // 处理不同数据类型的值，使其看起来像SQL字面量
                string valueString;
                switch (param.SqlDbType)
                {
                    case SqlDbType.NVarChar:
                    case SqlDbType.NChar:
                    case SqlDbType.VarChar:
                    case SqlDbType.Char:
                    case SqlDbType.Text:
                    case SqlDbType.NText:
                    case SqlDbType.Xml:
                        // 字符串需要用单引号包裹，并转义内部的单引号
                        valueString = $"'{param.Value.ToString().Replace("'", "''")}'";
                        break;

                    case SqlDbType.DateTime:
                    case SqlDbType.SmallDateTime:
                    case SqlDbType.Date:
                    case SqlDbType.Time:
                    case SqlDbType.DateTime2:
                        // 日期/时间需要用单引号包裹
                        valueString =
                            $"'{((DateTime)param.Value).ToString("yyyy-MM-dd HH:mm:ss.fff")}'";
                        break;

                    case SqlDbType.Bit:
                        // 布尔值转换为 1 或 0
                        valueString = Convert.ToBoolean(param.Value) ? "1" : "0";
                        break;

                    case SqlDbType.UniqueIdentifier:
                        valueString = $"'{param.Value.ToString()}'";
                        break;

                    // 数字类型直接使用其字符串表示
                    case SqlDbType.Int:
                    case SqlDbType.BigInt:
                    case SqlDbType.SmallInt:
                    case SqlDbType.TinyInt:
                    case SqlDbType.Decimal:
                    case SqlDbType.Float:
                    case SqlDbType.Real:
                    case SqlDbType.Money:
                    case SqlDbType.SmallMoney:
                        valueString = param.Value.ToString();
                        break;

                    default:
                        // 其他类型（如二进制）或未知类型也当作字符串处理
                        valueString = $"'{param.Value.ToString()}'";
                        break;
                }

                // 特殊处理 DBNull
                if (param.Value == DBNull.Value || param.Value == null)
                {
                    valueString = "NULL";
                }

                // 替换参数占位符。注意，这是一种简单的替换，对于复杂的SQL可能不完美。
                sb.Replace(param.ParameterName, valueString);
            }

            return sb.ToString();
        }
    }
}
