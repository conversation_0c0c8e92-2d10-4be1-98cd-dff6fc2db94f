<?xml version="1.0" encoding="utf-8"?>
<AutoCheckConfiguration>
  <!-- 自动核查时间设置 -->
  <Schedule>
    <!-- 每天执行的小时数 (0-23) -->
    <Hour>8</Hour>
    <!-- 每天执行的分钟数 (0-59) -->
    <Minute>0</Minute>
    <!-- 是否启用自动核查 -->
    <Enabled>true</Enabled>
  </Schedule>
  
  <!-- 报表输出设置 -->
  <ReportSettings>
    <!-- 报表输出目录 -->
    <OutputDirectory>C:\Reports\AutoCheck\</OutputDirectory>
    <!-- 文件名前缀 -->
    <FileNamePrefix>GUSM_AutoSOSCHECK</FileNamePrefix>
  </ReportSettings>
  
  <!-- 日志设置 -->
  <LogSettings>
    <!-- 是否记录详细日志 -->
    <VerboseLogging>true</VerboseLogging>
    <!-- 是否保留处理历史 -->
    <KeepHistory>true</KeepHistory>
  </LogSettings>
</AutoCheckConfiguration> 