﻿using System.IO;
using log4net.Core;
using log4net.Layout.Pattern;

namespace FlowCheck_DPLNG
{
    public class FileNamePatternConverter : PatternLayoutConverter
    {
        protected override void Convert(TextWriter writer, LoggingEvent loggingEvent)
        {
            if (loggingEvent.LocationInformation != null)
            {
                string fileName = Path.GetFileName(loggingEvent.LocationInformation.FileName);
                writer.Write($"{fileName}:{loggingEvent.LocationInformation.LineNumber}");
            }
        }
    }
}