using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using log4net;

namespace FlowCheck_DPLNG
{
    /// <summary>
    /// 性能监控器 - 收集和报告AutomaticCheckService的性能指标
    /// </summary>
    public class PerformanceMonitor : IDisposable
    {
        private static readonly ILog Log = LogManager.GetLogger(typeof(PerformanceMonitor));
        
        private readonly ConcurrentDictionary<string, PerformanceMetrics> _deviceMetrics = new ConcurrentDictionary<string, PerformanceMetrics>();
        private readonly ConcurrentDictionary<string, long> _operationCounters = new ConcurrentDictionary<string, long>();
        private readonly Timer _reportingTimer;
        private readonly object _lockObject = new object();
        private bool _disposed = false;

        // 性能统计
        private long _totalDevicesProcessed = 0;
        private long _totalSuccessfulDevices = 0;
        private long _totalFailedDevices = 0;
        private long _totalDatabaseQueries = 0;
        private long _totalAGA10Calculations = 0;
        private long _totalExcelReports = 0;

        public PerformanceMonitor(TimeSpan? reportingInterval = null)
        {
            var interval = reportingInterval ?? TimeSpan.FromMinutes(1);
            _reportingTimer = new Timer(ReportMetrics, null, interval, interval);
            
            Log.Info($"PerformanceMonitor initialized with reporting interval: {interval}");
        }

        /// <summary>
        /// 记录设备处理性能
        /// </summary>
        public void RecordDeviceProcessing(string deviceTag, TimeSpan duration, bool success)
        {
            _deviceMetrics.AddOrUpdate(deviceTag, 
                new PerformanceMetrics 
                { 
                    TotalRequests = 1, 
                    SuccessfulRequests = success ? 1 : 0, 
                    TotalDuration = duration,
                    MinDuration = duration,
                    MaxDuration = duration,
                    LastProcessTime = DateTime.Now
                },
                (key, existing) => 
                {
                    var newMetrics = new PerformanceMetrics
                    {
                        TotalRequests = existing.TotalRequests + 1,
                        SuccessfulRequests = existing.SuccessfulRequests + (success ? 1 : 0),
                        TotalDuration = existing.TotalDuration + duration,
                        MinDuration = duration < existing.MinDuration ? duration : existing.MinDuration,
                        MaxDuration = duration > existing.MaxDuration ? duration : existing.MaxDuration,
                        LastProcessTime = DateTime.Now
                    };
                    return newMetrics;
                });

            // 更新全局计数器
            Interlocked.Increment(ref _totalDevicesProcessed);
            if (success)
                Interlocked.Increment(ref _totalSuccessfulDevices);
            else
                Interlocked.Increment(ref _totalFailedDevices);
        }

        /// <summary>
        /// 记录数据库操作
        /// </summary>
        public void RecordDatabaseOperation(string operationType, TimeSpan duration, bool success)
        {
            var key = $"DB_{operationType}";
            IncrementCounter(key);
            Interlocked.Increment(ref _totalDatabaseQueries);
            
            Log.Debug($"Database operation recorded: {operationType}, Duration: {duration.TotalMilliseconds:F2}ms, Success: {success}");
        }

        /// <summary>
        /// 记录AGA10计算
        /// </summary>
        public void RecordAGA10Calculation(string deviceTag, TimeSpan duration, bool success)
        {
            IncrementCounter("AGA10_Calculations");
            Interlocked.Increment(ref _totalAGA10Calculations);
            
            Log.Debug($"AGA10 calculation recorded: {deviceTag}, Duration: {duration.TotalMilliseconds:F2}ms, Success: {success}");
        }

        /// <summary>
        /// 记录Excel报告生成
        /// </summary>
        public void RecordExcelGeneration(string deviceTag, TimeSpan duration, bool success)
        {
            IncrementCounter("Excel_Reports");
            Interlocked.Increment(ref _totalExcelReports);
            
            Log.Debug($"Excel generation recorded: {deviceTag}, Duration: {duration.TotalMilliseconds:F2}ms, Success: {success}");
        }

        /// <summary>
        /// 获取性能摘要
        /// </summary>
        public PerformanceSummary GetSummary()
        {
            lock (_lockObject)
            {
                var deviceCount = _deviceMetrics.Count;
                var totalRequests = _deviceMetrics.Values.Sum(m => m.TotalRequests);
                var successfulRequests = _deviceMetrics.Values.Sum(m => m.SuccessfulRequests);
                var totalDuration = _deviceMetrics.Values.Sum(m => m.TotalDuration.TotalMilliseconds);
                var averageDuration = totalRequests > 0 ? totalDuration / totalRequests : 0;

                return new PerformanceSummary
                {
                    TotalDevices = deviceCount,
                    TotalRequests = totalRequests,
                    SuccessfulRequests = successfulRequests,
                    FailedRequests = totalRequests - successfulRequests,
                    SuccessRate = totalRequests > 0 ? (double)successfulRequests / totalRequests : 0,
                    AverageDurationMs = averageDuration,
                    TotalDatabaseQueries = _totalDatabaseQueries,
                    TotalAGA10Calculations = _totalAGA10Calculations,
                    TotalExcelReports = _totalExcelReports,
                    ReportTime = DateTime.Now
                };
            }
        }

        /// <summary>
        /// 获取设备详细性能指标
        /// </summary>
        public PerformanceMetrics GetDeviceMetrics(string deviceTag)
        {
            return _deviceMetrics.TryGetValue(deviceTag, out var metrics) ? metrics : null;
        }

        /// <summary>
        /// 重置所有统计数据
        /// </summary>
        public void Reset()
        {
            lock (_lockObject)
            {
                _deviceMetrics.Clear();
                _operationCounters.Clear();
                
                _totalDevicesProcessed = 0;
                _totalSuccessfulDevices = 0;
                _totalFailedDevices = 0;
                _totalDatabaseQueries = 0;
                _totalAGA10Calculations = 0;
                _totalExcelReports = 0;
                
                Log.Info("Performance metrics reset");
            }
        }

        /// <summary>
        /// 定期报告性能指标
        /// </summary>
        private void ReportMetrics(object state)
        {
            try
            {
                var summary = GetSummary();
                
                Log.Info($"=== Performance Report ===");
                Log.Info($"Total Devices: {summary.TotalDevices}");
                Log.Info($"Total Requests: {summary.TotalRequests}");
                Log.Info($"Success Rate: {summary.SuccessRate:P2}");
                Log.Info($"Average Duration: {summary.AverageDurationMs:F2}ms");
                Log.Info($"Database Queries: {summary.TotalDatabaseQueries}");
                Log.Info($"AGA10 Calculations: {summary.TotalAGA10Calculations}");
                Log.Info($"Excel Reports: {summary.TotalExcelReports}");
                
                // 报告最慢的设备
                var slowestDevices = _deviceMetrics
                    .Where(kvp => kvp.Value.TotalRequests > 0)
                    .OrderByDescending(kvp => kvp.Value.TotalDuration.TotalMilliseconds / kvp.Value.TotalRequests)
                    .Take(5)
                    .ToList();
                
                if (slowestDevices.Any())
                {
                    Log.Info("=== Slowest Devices ===");
                    foreach (var device in slowestDevices)
                    {
                        var avgDuration = device.Value.TotalDuration.TotalMilliseconds / device.Value.TotalRequests;
                        Log.Info($"{device.Key}: {avgDuration:F2}ms avg, {device.Value.TotalRequests} requests");
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error reporting performance metrics: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 增加操作计数器
        /// </summary>
        private void IncrementCounter(string key)
        {
            _operationCounters.AddOrUpdate(key, 1, (k, v) => v + 1);
        }

        #region IDisposable Implementation

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    Log.Info("Disposing PerformanceMonitor...");
                    
                    _reportingTimer?.Dispose();
                    
                    // 最后一次报告
                    ReportMetrics(null);
                    
                    Log.Info("PerformanceMonitor disposed");
                }
                
                _disposed = true;
            }
        }

        #endregion
    }

    /// <summary>
    /// 设备性能指标
    /// </summary>
    public class PerformanceMetrics
    {
        public int TotalRequests { get; set; }
        public int SuccessfulRequests { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public TimeSpan MinDuration { get; set; }
        public TimeSpan MaxDuration { get; set; }
        public DateTime LastProcessTime { get; set; }
        
        public double SuccessRate => TotalRequests > 0 ? (double)SuccessfulRequests / TotalRequests : 0;
        public double AverageDurationMs => TotalRequests > 0 ? TotalDuration.TotalMilliseconds / TotalRequests : 0;
    }

    /// <summary>
    /// 性能摘要
    /// </summary>
    public class PerformanceSummary
    {
        public int TotalDevices { get; set; }
        public int TotalRequests { get; set; }
        public int SuccessfulRequests { get; set; }
        public int FailedRequests { get; set; }
        public double SuccessRate { get; set; }
        public double AverageDurationMs { get; set; }
        public long TotalDatabaseQueries { get; set; }
        public long TotalAGA10Calculations { get; set; }
        public long TotalExcelReports { get; set; }
        public DateTime ReportTime { get; set; }
    }
}
