﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using log4net;

namespace FlowCheck_DPLNG
{
    public class DataProcessor
    {
        private static readonly ILog Log = LogManager.GetLogger(typeof(DataProcessor));

        public static async Task<List<Dictionary<string, string>>> ReadDataInTimeRangeAsync(string connectionString,
            DateTime targetTime,
            int minutesRange, string dataTable)
        {
            try
            {
                return await Task.Run(() =>
                {
                    var result = new List<Dictionary<string, string>>();
                    var startTime = targetTime.AddMinutes(-minutesRange);

                    Log.Info(
                        $"Start to get data from database. TargetTime: {targetTime}，Range: {minutesRange} minutes");


                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        connection.Open();
                        Log.Info("Connected to database!");

                        string sql = $@"
                SELECT *
                FROM {dataTable}
                WHERE LocalTimeCol BETWEEN @StartTime AND @EndTime
                ORDER BY LocalTimeCol";

                        using (SqlCommand command = new SqlCommand(sql, connection))
                        {
                            command.Parameters.AddWithValue("@StartTime", startTime);
                            command.Parameters.AddWithValue("@EndTime", targetTime);

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                var schemaTable = reader.GetSchemaTable();
                                var columnNames = new List<string>();

                                foreach (DataRow row in schemaTable.Rows)
                                {
                                    columnNames.Add(row["ColumnName"].ToString());
                                }

                                while (reader.Read())
                                {
                                    var data = new Dictionary<string, string>();
                                    foreach (var columnName in columnNames)
                                    {
                                        data[columnName] = reader[columnName].ToString();
                                    }

                                    result.Add(data);
                                }
                            }
                        }
                    }

                    Log.Info($"Successfully get data from database. Count: {result.Count}");


                    return result;
                });
            }
            catch (Exception ex)
            {
                Log.Error($"Unknown ERROR：{ex.Message}");
                throw new MethodException("ReadDataInTimeRange", $"Error: {ex.Message}", ex);
            }
        }


        //public static async Task<(bool isStable, List<string> unstableFields, Dictionary<string, FieldStatistics>
        //        fieldStats)>
        //    CheckDataStabilityAsync(
        //        List<Dictionary<string, string>> data,
        //        Action<string, bool> fieldStatusCallback,
        //        Dictionary<string, FieldTypeStatus> fieldTypeStatuses) // 新增参数
        //{
        //    Log.Info("Start to check stability!");
        //    Debug.WriteLine("Starting CheckDataStabilityAsync");

        //    var unstableFields = new List<string>();
        //    var fieldStats = new Dictionary<string, FieldStatistics>();

        //    try
        //    {
        //        // 第一遍遍历：计算基本统计量
        //        foreach (var record in data)
        //        {
        //            foreach (var field in record.Keys)
        //            {
        //                // 计算每种字段类型的总数
        //                foreach (var fieldType in fieldTypeStatuses.Keys)
        //                {
        //                    if (field.Contains(fieldType))
        //                    {
        //                        fieldTypeStatuses[fieldType].TotalFields++;
        //                        break;
        //                    }
        //                }

        //                if (field.Contains("SLCT") || field.Contains("PressInuse_Check") ||
        //                    field.Contains("TempInuse_Check") || field.Contains("USMAVGVOS_Check"))
        //                {
        //                    if (double.TryParse(record[field], out double value))
        //                    {
        //                        if (!fieldStats.ContainsKey(field))
        //                        {
        //                            fieldStats[field] = new FieldStatistics
        //                            {
        //                                Min = value,
        //                                Max = value,
        //                                Sum = value,
        //                                Count = 1,
        //                                Values = new List<double> { value }
        //                            };
        //                        }
        //                        else
        //                        {
        //                            var stats = fieldStats[field];
        //                            stats.Min = Math.Min(stats.Min, value);
        //                            stats.Max = Math.Max(stats.Max, value);
        //                            stats.Sum += value;
        //                            stats.Count++;
        //                            stats.Values.Add(value);
        //                        }
        //                    }
        //                    else
        //                    {
        //                        Log.Warn($"Cannot get the value of {field}: {record[field]}");
        //                    }
        //                }
        //            }
        //        }

        //        // 第二遍遍历：计算标准差和判断稳定性
        //        foreach (var field in fieldStats.Keys)
        //        {
        //            var stats = fieldStats[field];
        //            stats.Range = stats.Max - stats.Min;
        //            stats.Average = stats.Sum / stats.Count;

        //            // 计算标准差
        //            double sumOfSquares = stats.Values.Sum(v => Math.Pow(v - stats.Average, 2));
        //            stats.StandardDeviation = Math.Sqrt(sumOfSquares / stats.Count);

        //            bool isUnstable = false;
        //            if (field.Contains("SLCT") && (stats.Range > 0.1 || stats.StandardDeviation > 0.03))
        //                isUnstable = true;
        //            else if (field.Contains("PressInuse_Check") && (stats.Range > 500 || stats.StandardDeviation > 50))
        //                isUnstable = true;
        //            else if (field.Contains("TempInuse_Check") && (stats.Range > 0.5 || stats.StandardDeviation > 0.1))
        //                isUnstable = true;
        //            else if (field.Contains("USMAVGVOS_Check") && (stats.Range > 0.5 || stats.StandardDeviation > 0.15))
        //                isUnstable = true;

        //            if (isUnstable)
        //            {
        //                unstableFields.Add(field);
        //                Log.Warn(
        //                    $"Stability Check FAILED: {field}，Range: {stats.Range}, StdDev: {stats.StandardDeviation}");
        //            }

        //            bool isFieldStable = !isUnstable;
        //            await Task.Run(() => fieldStatusCallback(field, isFieldStable));

        //            if (isUnstable)
        //            {
        //                unstableFields.Add(field);
        //                Log.Warn(
        //                    $"Stability Check FAILED: {field}, Range: {stats.Range}, StdDev: {stats.StandardDeviation}");
        //            }

        //            // 等待1秒
        //            await Task.Delay(100);
        //        }

        //        Log.Info($"Stability Check finished. Unstable Count: {unstableFields.Count}");
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Error($"Stability Check ERROR: {ex.Message}");
        //        throw new MethodException("CheckDataStabilityAsync", $"Error: {ex.Message}", ex);
        //    }

        //    bool isAllStable = unstableFields.Count == 0;
        //    Log.Info($"CheckDataStabilityAsync completed. Is all stable: {isAllStable}");
        //    return (isAllStable, unstableFields, fieldStats);
        //}

        public static async Task<(bool isStable, List<string> unstableFields, Dictionary<string, FieldStatistics>
                fieldStats)>
            CheckDataStabilityAsync(
                List<Dictionary<string, string>> data,
                Action<string, bool> fieldStatusCallback,
                Dictionary<string, FieldTypeStatus> fieldTypeStatuses,
                Dictionary<string, double> rangeThresholds,
                Dictionary<string, double> stdDevThresholds
            )
        {
            Log.Info("Start to check stability!");
            Debug.WriteLine("Starting CheckDataStabilityAsync");

            var unstableFields = new List<string>();
            var fieldStats = new Dictionary<string, FieldStatistics>();

            try
            {
                // 第一遍遍历：计算基本统计量
                foreach (var record in data)
                {
                    foreach (var field in record.Keys)
                    {
                        // 计算每种字段类型的总数
                        foreach (var fieldType in fieldTypeStatuses.Keys)
                        {
                            if (field.Contains(fieldType))
                            {
                                fieldTypeStatuses[fieldType].TotalFields++;
                                break;
                            }
                        }

                        if (field.Contains("SLCT") || field.Contains("PressInuse") ||
                            field.Contains("TempInuse") || field.Contains("USMAvgVOS"))
                        {
                            if (double.TryParse(record[field], out double value))
                            {
                                if (!fieldStats.ContainsKey(field))
                                {
                                    fieldStats[field] = new FieldStatistics
                                    {
                                        Min = value,
                                        Max = value,
                                        Sum = value,
                                        Count = 1,
                                        Values = new List<double> { value }
                                    };
                                }
                                else
                                {
                                    var stats = fieldStats[field];
                                    stats.Min = Math.Min(stats.Min, value);
                                    stats.Max = Math.Max(stats.Max, value);
                                    stats.Sum += value;
                                    stats.Count++;
                                    stats.Values.Add(value);
                                }
                            }
                            else
                            {
                                Log.Warn($"Cannot get the value of {field}: {record[field]}");
                            }
                        }
                    }
                }

                // 第二遍遍历：计算标准差和判断稳定性
                foreach (var field in fieldStats.Keys)
                {
                    var stats = fieldStats[field];
                    stats.Range = stats.Max - stats.Min;
                    stats.Average = stats.Sum / stats.Count;

                    // 计算标准差
                    double sumOfSquares = stats.Values.Sum(v => Math.Pow(v - stats.Average, 2));
                    stats.StandardDeviation = Math.Sqrt(sumOfSquares / stats.Count);

                    bool isUnstable = false;

                    // 使用新的阈值进行判断
                    string fieldKey = GetFieldKey(field);
                    if (rangeThresholds.TryGetValue(fieldKey, out double rangeThreshold) &&
                        stdDevThresholds.TryGetValue(fieldKey, out double stdDevThreshold))
                    {
                        if (stats.Range > rangeThreshold || stats.StandardDeviation > stdDevThreshold)
                            isUnstable = true;
                    }
                    else
                    {
                        // 如果没有找到对应的阈值，使用默认值
                        if (field.Contains("SLCT") && (stats.Range > 0.1 || stats.StandardDeviation > 0.03))
                            isUnstable = true;
                        else if (field.Contains("PressInuse") &&
                                 (stats.Range > 500 || stats.StandardDeviation > 50))
                            isUnstable = true;
                        else if (field.Contains("TempInuse") &&
                                 (stats.Range > 0.5 || stats.StandardDeviation > 0.1))
                            isUnstable = true;
                        else if (field.Contains("USMAvgVOS") &&
                                 (stats.Range > 0.5 || stats.StandardDeviation > 0.15))
                            isUnstable = true;
                    }

                    if (isUnstable)
                    {
                        unstableFields.Add(field);
                        Log.Warn(
                            $"Stability Check FAILED: {field}，Range: {stats.Range}, StdDev: {stats.StandardDeviation}");
                    }

                    bool isFieldStable = !isUnstable;
                    await Task.Run(() => fieldStatusCallback(field, isFieldStable));

                    // 等待1秒
                    await Task.Delay(100);
                }

                Log.Info($"Stability Check finished. Unstable Count: {unstableFields.Count}");
            }
            catch (Exception ex)
            {
                Log.Error($"Stability Check ERROR: {ex.Message}");
                throw new MethodException("CheckDataStabilityAsync", $"Error: {ex.Message}", ex);
            }

            bool isAllStable = unstableFields.Count == 0;
            Log.Info($"CheckDataStabilityAsync completed. Is all stable: {isAllStable}");
            return (isAllStable, unstableFields, fieldStats);
        }

        // 辅助方法来获取字段的关键字
        private static string GetFieldKey(string field)
        {
            if (field.Contains("SLCT")) return "SLCT";
            if (field.Contains("PressInuse")) return "PressInuse";
            if (field.Contains("TempInuse")) return "TempInuse";
            if (field.Contains("USMAvgVOS")) return "USMAvgVOS";
            return field;
        }

        public class FieldStatistics
        {
            public double Min { get; set; }
            public double Max { get; set; }
            public double Sum { get; set; }
            public int Count { get; set; }
            public double Range { get; set; }
            public double Average { get; set; }
            public double StandardDeviation { get; set; }
            public List<double> Values { get; set; }
        }
    }
}