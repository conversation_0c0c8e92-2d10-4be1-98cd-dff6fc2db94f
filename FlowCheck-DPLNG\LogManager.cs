﻿using System;
using System.IO;
using System.Reflection;
using log4net;
using log4net.Config;

namespace FlowCheck_DPLNG
{
    public static class LogManager
    {
        private static readonly ILog _log = log4net.LogManager.GetLogger(typeof(LogManager));

        static LogManager()
        {
            // 加载 log4net 配置
            var logRepository = log4net.LogManager.GetRepository(Assembly.GetEntryAssembly());
            XmlConfigurator.Configure(logRepository, new FileInfo("log4net.config"));
        }

        public static ILog GetLogger(Type type)
        {
            return log4net.LogManager.GetLogger(type);
        }

        public static void LogDebug(object message)
        {
            _log.Debug(message);
        }

        public static void LogInfo(object message)
        {
            _log.Info(message);
        }

        public static void LogWarn(object message)
        {
            _log.Warn(message);
        }

        public static void LogError(object message)
        {
            _log.Error(message);
        }

        public static void LogFatal(object message)
        {
            _log.Fatal(message);
        }

        // 可以根据需要添加更多的日志方法，例如包含异常信息的方法
        public static void LogError(object message, Exception exception)
        {
            _log.Error(message, exception);
        }
    }
}