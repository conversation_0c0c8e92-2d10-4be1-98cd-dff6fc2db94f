﻿using System;

namespace FlowCheck_DPLNG
{
    internal class GUSM
    {
        public float IntDiam { get; set; }
        public float ExtDiam { get; set; }
        public string FwVer { get; set; }

        private double youngsMoudle;

        public double YoungsMoudle
        {
            get { return youngsMoudle; }
            set
            {
                if (value == double.Parse("6.09"))
                {
                    youngsMoudle = 206843000;
                }
            }
        }

        public double[] FlowCalculator(double intDiam, double extDiam, double tempCoeff, double Tf, double Pf, double YoungModulus, double Df, double Ds, double CV, double gasVel)
        {
            try
            {
                double pi = Math.PI;

                double area = pi * intDiam * intDiam * 0.25;
                double strain = (1 / YoungModulus) * (1.3 * extDiam * extDiam + 0.4 * intDiam * intDiam) / (extDiam * extDiam - intDiam * intDiam);
                double ctsm = 1 + (3 * tempCoeff * (Tf - 20));
                double cpsm = 1 + (3 * strain * (Pf - 101.325));

                double GVOL = gasVel * 3600 * area * ctsm * cpsm;
                double SVOL = GVOL * (Df / Ds);
                double MassFR = GVOL * Df / 1000;
                double EnergyFR = SVOL * CV / 1000;

                double[] arrayResult = new double[] { GVOL, SVOL, MassFR, EnergyFR };
                return arrayResult;
            }
            catch (Exception)
            {
                throw;
            }
        }
    }
}