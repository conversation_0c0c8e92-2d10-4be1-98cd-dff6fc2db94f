using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using log4net;

namespace FlowCheck_DPLNG
{
    /// <summary>
    /// AutomaticCheckService性能基准测试
    /// </summary>
    public class PerformanceBenchmark
    {
        private static readonly ILog Log = LogManager.GetLogger(typeof(PerformanceBenchmark));
        
        private readonly AutomaticCheckService _service;
        private readonly List<DeviceCombination> _testDevices;

        public PerformanceBenchmark(AutomaticCheckService service)
        {
            _service = service ?? throw new ArgumentNullException(nameof(service));
            _testDevices = GenerateTestDevices();
        }

        /// <summary>
        /// 运行性能基准测试
        /// </summary>
        public async Task<BenchmarkResult> RunBenchmarkAsync(int deviceCount = 10)
        {
            Log.Info($"Starting performance benchmark with {deviceCount} devices");
            
            var stopwatch = Stopwatch.StartNew();
            var testDevices = _testDevices.Take(deviceCount).ToList();

            var startMemory = GC.GetTotalMemory(true);
            var startTime = DateTime.Now;

            try
            {
                // 执行优化的批处理测试
                var (successCount, failCount) = await TestOptimizedBatchProcessingAsync(testDevices, startTime);

                stopwatch.Stop();
                var endMemory = GC.GetTotalMemory(true);

                var result = new BenchmarkResult
                {
                    DeviceCount = deviceCount,
                    TotalTime = stopwatch.Elapsed,
                    SuccessCount = successCount,
                    FailureCount = failCount,
                    MemoryUsed = endMemory - startMemory,
                    AverageTimePerDevice = stopwatch.Elapsed.TotalMilliseconds / deviceCount,
                    ThroughputDevicesPerMinute = deviceCount / stopwatch.Elapsed.TotalMinutes,
                    TestTime = DateTime.Now
                };

                Log.Info($"Benchmark completed: {result}");
                return result;
            }
            catch (Exception ex)
            {
                Log.Error($"Benchmark failed: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 测试优化的批处理性能
        /// </summary>
        private async Task<(int success, int fail)> TestOptimizedBatchProcessingAsync(
            List<DeviceCombination> devices, DateTime checkTime)
        {
            try
            {
                // 使用反射调用私有方法进行测试
                var method = typeof(AutomaticCheckService).GetMethod("ExecuteOptimizedBatchProcessingAsync", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                if (method != null)
                {
                    var task = (Task<(int, int)>)method.Invoke(_service, new object[] { devices, checkTime });
                    return await task;
                }
                else
                {
                    Log.Error("Could not find ExecuteOptimizedBatchProcessingAsync method");
                    return (0, devices.Count);
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error in optimized batch processing test: {ex.Message}", ex);
                return (0, devices.Count);
            }
        }

        /// <summary>
        /// 生成测试设备数据
        /// </summary>
        private List<DeviceCombination> GenerateTestDevices()
        {
            var devices = new List<DeviceCombination>();
            
            // 生成测试设备组合
            for (int i = 1; i <= 100; i++)
            {
                devices.Add(new DeviceCombination
                {
                    Station = $"TestStation_{i % 10 + 1}",
                    FlowMeter = $"FT_{i:D5}",
                    FlowComputer = $"FC_{i:D5}"
                });
            }
            
            Log.Info($"Generated {devices.Count} test devices");
            return devices;
        }

        /// <summary>
        /// 比较优化前后的性能
        /// </summary>
        public async Task<ComparisonResult> ComparePerformanceAsync(int deviceCount = 10)
        {
            Log.Info($"Starting performance comparison with {deviceCount} devices");

            // 运行优化版本测试
            var optimizedResult = await RunBenchmarkAsync(deviceCount);

            // 估算原始版本的性能（基于分析报告的数据）
            var originalEstimate = EstimateOriginalPerformance(deviceCount);

            var comparison = new ComparisonResult
            {
                DeviceCount = deviceCount,
                OptimizedResult = optimizedResult,
                OriginalEstimate = originalEstimate,
                TimeImprovement = originalEstimate.TotalTime.TotalMilliseconds / optimizedResult.TotalTime.TotalMilliseconds,
                ThroughputImprovement = optimizedResult.ThroughputDevicesPerMinute / originalEstimate.ThroughputDevicesPerMinute
            };

            Log.Info($"Performance comparison completed:");
            Log.Info($"  Time improvement: {comparison.TimeImprovement:F2}x faster");
            Log.Info($"  Throughput improvement: {comparison.ThroughputImprovement:F2}x higher");

            return comparison;
        }

        /// <summary>
        /// 估算原始版本的性能（基于分析报告）
        /// </summary>
        private BenchmarkResult EstimateOriginalPerformance(int deviceCount)
        {
            // 基于分析报告：每设备7-11秒 + 1秒延迟 = 8-12秒
            var avgTimePerDevice = 9.0; // 秒
            var totalTimeSeconds = deviceCount * avgTimePerDevice;

            return new BenchmarkResult
            {
                DeviceCount = deviceCount,
                TotalTime = TimeSpan.FromSeconds(totalTimeSeconds),
                SuccessCount = deviceCount, // 假设全部成功
                FailureCount = 0,
                MemoryUsed = deviceCount * 2 * 1024 * 1024, // 估算每设备2MB内存
                AverageTimePerDevice = avgTimePerDevice * 1000, // 转换为毫秒
                ThroughputDevicesPerMinute = 60.0 / avgTimePerDevice,
                TestTime = DateTime.Now
            };
        }

        /// <summary>
        /// 运行压力测试
        /// </summary>
        public async Task<List<BenchmarkResult>> RunStressTestAsync()
        {
            var results = new List<BenchmarkResult>();
            var deviceCounts = new[] { 1, 5, 10, 20, 50, 100 };

            Log.Info("Starting stress test with varying device counts");

            foreach (var count in deviceCounts)
            {
                try
                {
                    Log.Info($"Running stress test with {count} devices");
                    var result = await RunBenchmarkAsync(count);
                    results.Add(result);
                    
                    // 短暂休息，避免系统过载
                    await Task.Delay(2000);
                }
                catch (Exception ex)
                {
                    Log.Error($"Stress test failed for {count} devices: {ex.Message}", ex);
                }
            }

            Log.Info($"Stress test completed with {results.Count} successful runs");
            return results;
        }

        /// <summary>
        /// 生成性能报告
        /// </summary>
        public string GeneratePerformanceReport(List<BenchmarkResult> results)
        {
            if (results == null || !results.Any())
                return "No benchmark results available.";

            var report = new System.Text.StringBuilder();
            report.AppendLine("=== AutomaticCheckService Performance Benchmark Report ===");
            report.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();

            report.AppendLine("Device Count | Total Time | Success Rate | Avg Time/Device | Throughput | Memory Used");
            report.AppendLine("-------------|------------|--------------|-----------------|------------|------------");

            foreach (var result in results.OrderBy(r => r.DeviceCount))
            {
                var successRate = result.DeviceCount > 0 ? (double)result.SuccessCount / result.DeviceCount : 0;
                report.AppendLine($"{result.DeviceCount,11} | {result.TotalTime.TotalMinutes:F2}m | {successRate:P2} | {result.AverageTimePerDevice:F0}ms | {result.ThroughputDevicesPerMinute:F1}/min | {result.MemoryUsed / 1024 / 1024:F1}MB");
            }

            report.AppendLine();
            report.AppendLine("=== Performance Analysis ===");

            if (results.Count > 1)
            {
                var best = results.OrderBy(r => r.AverageTimePerDevice).First();
                var worst = results.OrderByDescending(r => r.AverageTimePerDevice).First();

                report.AppendLine($"Best performance: {best.DeviceCount} devices in {best.AverageTimePerDevice:F0}ms/device");
                report.AppendLine($"Worst performance: {worst.DeviceCount} devices in {worst.AverageTimePerDevice:F0}ms/device");
                
                var scalability = worst.AverageTimePerDevice / best.AverageTimePerDevice;
                report.AppendLine($"Scalability factor: {scalability:F2}x (lower is better)");
            }

            return report.ToString();
        }
    }

    /// <summary>
    /// 基准测试结果
    /// </summary>
    public class BenchmarkResult
    {
        public int DeviceCount { get; set; }
        public TimeSpan TotalTime { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public long MemoryUsed { get; set; }
        public double AverageTimePerDevice { get; set; }
        public double ThroughputDevicesPerMinute { get; set; }
        public DateTime TestTime { get; set; }

        public override string ToString()
        {
            var successRate = DeviceCount > 0 ? (double)SuccessCount / DeviceCount : 0;
            return $"Benchmark Results:\n" +
                   $"  Devices: {DeviceCount}\n" +
                   $"  Total Time: {TotalTime:hh\\:mm\\:ss}\n" +
                   $"  Success Rate: {successRate:P2}\n" +
                   $"  Memory Used: {MemoryUsed / 1024 / 1024:F2} MB\n" +
                   $"  Avg Time/Device: {AverageTimePerDevice:F2} ms\n" +
                   $"  Throughput: {ThroughputDevicesPerMinute:F2} devices/min";
        }
    }

    /// <summary>
    /// 性能比较结果
    /// </summary>
    public class ComparisonResult
    {
        public int DeviceCount { get; set; }
        public BenchmarkResult OptimizedResult { get; set; }
        public BenchmarkResult OriginalEstimate { get; set; }
        public double TimeImprovement { get; set; }
        public double ThroughputImprovement { get; set; }
    }
}
