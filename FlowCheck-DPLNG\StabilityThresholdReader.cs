﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Xml.Linq;

namespace FlowCheck_DPLNG
{
    public class StabilityThresholdReader
    {
        private readonly string xmlPath;
        public Dictionary<string, double> RangeThresholds { get; private set; }
        public Dictionary<string, double> StdDevThresholds { get; private set; }

        public StabilityThresholdReader(string configPath = null)
        {
            xmlPath = configPath ??
                      Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "StabilityThresholds.xml");
            RangeThresholds = new Dictionary<string, double>();
            StdDevThresholds = new Dictionary<string, double>();
        }

        public void ReadThresholds()
        {
            if (!File.Exists(xmlPath))
            {
                return;
            }

            try
            {
                XDocument doc = XDocument.Load(xmlPath);

                if (doc.Root != null)
                    foreach (var field in doc.Root.Elements("Field"))
                    {
                        string name = field.Attribute("name")?.Value;
                        double rangeThreshold = double.Parse(field.Element("RangeThreshold")?.Value);
                        double stdDevThreshold = double.Parse(field.Element("StdDevThreshold")?.Value);

                        if (name != null)
                        {
                            RangeThresholds[name] = rangeThreshold;
                            StdDevThresholds[name] = stdDevThreshold;
                        }
                    }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GetRowsFromOE: Caught exception, ignoring: {ex.Message}");
            }
        }
    }
}