﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using Newtonsoft.Json;

namespace FlowCheck_DPLNG
{
    public class StationConfigManager
    {
        private static readonly string ConfigPath = Path.Combine(Application.StartupPath,
            "config",
            "stations.json");
        public class StationConfig
        {
            public List<Station> Stations { get; set; }
        }
        public class Station
        {
            public string Name { get; set; }
            public List<string> Streams { get; set; }
            public List<string> Fc { get; set; }
        }
        private StationConfig _config;
        public StationConfigManager()
        {
            LoadConfig();
        }
        private void LoadConfig()
        {
            try
            {
                if (!File.Exists(ConfigPath))
                {
                    throw new FileNotFoundException("配置文件不存在！");
                }
                string jsonContent = File.ReadAllText(ConfigPath);
                _config = JsonConvert.DeserializeObject<StationConfig>(jsonContent);
                if (_config?.Stations == null)
                {
                    throw new InvalidDataException("配置文件格式错误！");
                }
            }
            catch (Exception)
            {
                _config = new StationConfig { Stations = new List<Station>() };
                throw;
            }
        }
        public List<Station> GetAllStations()
        {
            return _config?.Stations ?? new List<Station>();
        }
        public Station GetStationByName(string stationName)
        {
            return _config?.Stations?.Find(s => s.Name == stationName);
        }
        public (List<string> streams, List<string> fc) GetStreamsByStation(string stationName)
        {
            var station = GetStationByName(stationName);
            return (
                station?.Streams ?? new List<string>(),
                station?.Fc ?? new List<string>()
            );
        }
        public void ReloadConfig()
        {
            LoadConfig();
        }
    }

}
