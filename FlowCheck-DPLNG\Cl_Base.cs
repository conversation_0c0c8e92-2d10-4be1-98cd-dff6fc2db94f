﻿using System;
using System.IO;
using System.Text;
using System.Xml;

namespace FlowCheck_DPLNG
{
    internal class Cl_Base
    {
        public static void getUserSet(out string ip, out string usercode, out string userpwd, out string db, out string times)
        {
            string baseDirectory = AppDomain.CurrentDomain.BaseDirectory + @"config/config.xml";
            XmlDocument doc = new XmlDocument();
            doc.Load(baseDirectory);
            XmlElement root = doc.DocumentElement;
            ip = root?.GetElementsByTagName("ip")[0].InnerText;
            usercode = root?.GetElementsByTagName("usercode")[0].InnerText;
            userpwd = root?.GetElementsByTagName("userpwd")[0].InnerText;
            db = root?.GetElementsByTagName("db")[0].InnerText;
            times = root?.GetElementsByTagName("times")[0].InnerText;
        }

        public static void updUserSet(string ip, string usercode, string userpwd, string db, string times)
        {
            string baseDirectory = AppDomain.CurrentDomain.BaseDirectory + @"config/config.xml";
            XmlDocument xdoc = new XmlDocument();
            xdoc.Load(baseDirectory);
            XmlElement root = xdoc.DocumentElement;
            root.GetElementsByTagName("ip")[0].InnerText = ip;
            root.GetElementsByTagName("usercode")[0].InnerText = usercode;
            userpwd = root.GetElementsByTagName("userpwd")[0].InnerText = userpwd;
            root.GetElementsByTagName("db")[0].InnerText = db;
            root.GetElementsByTagName("times")[0].InnerText = times;
            xdoc.Save(baseDirectory);
        }

        public static void getLinkSet(out string UserName, out string PassWord, out string StationName)
        {
            string pathaddress = AppDomain.CurrentDomain.BaseDirectory + @"config/config.xml";
            XmlDocument xdoc = new XmlDocument();
            xdoc.Load(pathaddress);
            XmlElement root = xdoc.DocumentElement;
            UserName = root.GetElementsByTagName("UserName")[0].InnerText;
            PassWord = root.GetElementsByTagName("PassWord")[0].InnerText;
            StationName = root.GetElementsByTagName("StationName")[0].InnerText;
        }

        public static void updLinkSet(string UserName, string PassWord, string StationName)
        {
            string pathaddress = AppDomain.CurrentDomain.BaseDirectory + @"config/config.xml";
            XmlDocument xdoc = new XmlDocument();
            xdoc.Load(pathaddress);
            XmlElement root = xdoc.DocumentElement;
            root.GetElementsByTagName("UserName")[0].InnerText = UserName;
            root.GetElementsByTagName("PassWord")[0].InnerText = PassWord;
            root.GetElementsByTagName("StationName")[0].InnerText = StationName;
            xdoc.Save(pathaddress);
        }

        public static void WriteTextLog(string action, string strMessage, DateTime time)
        {
            //string path = AppDomain.CurrentDomain.BaseDirectory + @"log\";
            string path = "C:\\FlowCheckLog\\";
            if (!Directory.Exists(path))
                Directory.CreateDirectory(path);

            string fileFullPath = path + time.ToString("yyyy-MM-dd") + ".txt";
            StringBuilder str = new StringBuilder();
            str.Append("Time:" + time.ToString() + "---Action:" + action + "---Message:" + strMessage + "\r\n");
            //str.Append("Action:" + action + "\r\n");
            //str.Append("Message:" + strMessage + "\r\n");
            str.Append("----------------------------------");
            StreamWriter sw;
            if (!File.Exists(fileFullPath))
            {
                sw = File.CreateText(fileFullPath);
            }
            else
            {
                sw = File.AppendText(fileFullPath);
            }
            sw.WriteLine(str.ToString());
            sw.Close();
        }

        /// <summary>
        /// 读取txt文件内容
        /// </summary>
        /// <param name="Path">文件地址</param>
        public static string ReadTxtContent(string Path)
        {
            string Content = string.Empty;
            if (File.Exists(Path))
            {
                StreamReader sr = new StreamReader(Path, Encoding.UTF8);
                String line;
                while ((line = sr.ReadLine()) != null)
                {
                    Content = Content + "\r\n" + line;
                }
                sr.Close();
            }
            return Content;
        }
    }
}