﻿using System;
using System.IO;

namespace FlowCheck_DPLNG
{
    internal class Log
    {
        public static void AddLog(string message)
        {
            // 在每次写日志时都获取当前日期作为文件名
            string logFileName = DateTime.Now.ToString("yyyy-MM-dd") + ".txt";

            try
            {
                // 追加一条记录到日志文件中
                using (StreamWriter writer = new StreamWriter(logFileName, true))
                {
                    writer.WriteLine($"{DateTime.Now}: {message}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Caught exception, ignoring: {ex.Message}");
            }
        }
    }
}