using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using log4net;

namespace FlowCheck_DPLNG
{
    /// <summary>
    /// 优化的数据库服务 - 提供连接池管理和批量查询功能
    /// </summary>
    public class OptimizedDatabaseService : IDisposable
    {
        private static readonly ILog Log = LogManager.GetLogger(typeof(OptimizedDatabaseService));
        
        private readonly string _connectionString;
        private readonly SemaphoreSlim _connectionSemaphore;
        private readonly PerformanceMonitor _performanceMonitor;
        private bool _disposed = false;

        // 数据库操作配置
        private const int DatabaseTimeoutSeconds = 30;
        private const int MaxRetryAttempts = 3;
        private const int RetryDelayMilliseconds = 1000;

        public OptimizedDatabaseService(string connectionString, int maxConcurrentConnections = 10, PerformanceMonitor performanceMonitor = null)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
            _connectionSemaphore = new SemaphoreSlim(maxConcurrentConnections);
            _performanceMonitor = performanceMonitor;
            
            Log.Info($"OptimizedDatabaseService initialized with max concurrent connections: {maxConcurrentConnections}");
        }

        /// <summary>
        /// 批量获取设备数据 - 使用单个连接处理多个设备
        /// </summary>
        public async Task<Dictionary<string, DeviceData>> GetBatchDeviceDataAsync(
            List<DeviceCombination> devices, DateTime checkTime)
        {
            if (devices == null || !devices.Any())
                return new Dictionary<string, DeviceData>();

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            await _connectionSemaphore.WaitAsync();
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                
                var results = new Dictionary<string, DeviceData>();
                
                foreach (var device in devices)
                {
                    try
                    {
                        // 使用同一个连接执行多个查询
                        var fcData = await GetFCDataInternal(connection, device.FlowComputer, checkTime);
                        var ftData = await GetFTDataInternal(connection, device.FlowMeter, checkTime);
                        var reportData = await GetReportDataInternal(connection, device.FlowMeter, checkTime, 10);
                        
                        results[device.FlowMeter] = new DeviceData
                        {
                            FCData = fcData,
                            FTData = ftData,
                            ReportData = reportData
                        };
                        
                        Log.Debug($"Retrieved data for device: {device.FlowMeter}");
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"Error retrieving data for device {device.FlowMeter}: {ex.Message}", ex);
                        // 继续处理其他设备
                    }
                }
                
                stopwatch.Stop();
                _performanceMonitor?.RecordDatabaseOperation("BatchDeviceData", stopwatch.Elapsed, true);
                
                Log.Info($"Batch retrieved data for {results.Count}/{devices.Count} devices in {stopwatch.ElapsedMilliseconds}ms");
                return results;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _performanceMonitor?.RecordDatabaseOperation("BatchDeviceData", stopwatch.Elapsed, false);
                Log.Error($"Error in batch device data retrieval: {ex.Message}", ex);
                throw;
            }
            finally
            {
                _connectionSemaphore.Release();
            }
        }

        /// <summary>
        /// 获取FC表数据（内部方法，复用连接）
        /// </summary>
        private async Task<Dictionary<string, string>> GetFCDataInternal(SqlConnection connection, string flowComputerTag, DateTime checkTime)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                var result = new Dictionary<string, string>();
                
                // 优化：只查询需要的字段
                var sqlFC = @"
                    DECLARE @InputDateTime DATETIME = CONVERT(DATETIME, @TargetTime, 101);

                    SELECT TOP 1 LocalTimeCol, SLCT_METHANE_1, SLCT_ETHANE_1, SLCT_PROPANE_1, 
                           SLCT_NITROGEN_1, SLCT_CO2_1, PressInuse_1, TempInuse_1
                    FROM {0}
                    WHERE LocalTimeCol <= @InputDateTime
                    ORDER BY LocalTimeCol DESC";
                
                sqlFC = string.Format(sqlFC, flowComputerTag);
                
                using var command = new SqlCommand(sqlFC, connection);
                command.CommandTimeout = DatabaseTimeoutSeconds;
                command.Parameters.AddWithValue("@TargetTime", checkTime);

                using var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    for (var i = 0; i < reader.FieldCount; i++)
                    {
                        result[reader.GetName(i)] = reader[i]?.ToString() ?? "";
                    }
                }
                
                stopwatch.Stop();
                _performanceMonitor?.RecordDatabaseOperation("GetFCData", stopwatch.Elapsed, result.Any());
                
                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _performanceMonitor?.RecordDatabaseOperation("GetFCData", stopwatch.Elapsed, false);
                Log.Error($"Error getting FC data for {flowComputerTag}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 获取FT表数据（内部方法，复用连接）
        /// </summary>
        private async Task<Dictionary<string, string>> GetFTDataInternal(SqlConnection connection, string flowMeterTag, DateTime checkTime)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                var result = new Dictionary<string, string>();
                
                // 优化：只查询需要的字段
                var sqlFT = @"
                    DECLARE @InputDateTime DATETIME = CONVERT(DATETIME, @TargetTime, 101);

                    SELECT TOP 1 LocalTimeCol, USMAvgVOS_1, PressInuse_1, TempInuse_1,
                           SLCT_METHANE_1, SLCT_ETHANE_1, SLCT_PROPANE_1, SLCT_NITROGEN_1, SLCT_CO2_1
                    FROM {0}
                    WHERE LocalTimeCol <= @InputDateTime
                    ORDER BY LocalTimeCol DESC";
                
                sqlFT = string.Format(sqlFT, flowMeterTag);
                
                using var command = new SqlCommand(sqlFT, connection);
                command.CommandTimeout = DatabaseTimeoutSeconds;
                command.Parameters.AddWithValue("@TargetTime", checkTime);
                
                using var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    for (var i = 0; i < reader.FieldCount; i++)
                    {
                        result[reader.GetName(i)] = reader[i]?.ToString() ?? "";
                    }
                }
                
                stopwatch.Stop();
                _performanceMonitor?.RecordDatabaseOperation("GetFTData", stopwatch.Elapsed, result.Any());
                
                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _performanceMonitor?.RecordDatabaseOperation("GetFTData", stopwatch.Elapsed, false);
                Log.Error($"Error getting FT data for {flowMeterTag}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 获取报告数据（内部方法，复用连接）- 流式处理优化
        /// </summary>
        private async Task<List<Dictionary<string, string>>> GetReportDataInternal(
            SqlConnection connection, string flowMeterTag, DateTime targetTime, int durationMinutes)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                var reportData = new List<Dictionary<string, string>>();
                const int BATCH_SIZE = 100; // 分批读取，减少内存压力
                
                // 优化：只查询需要的字段
                var sql = @"
                    SELECT LocalTimeCol, SLCT_METHANE_1, SLCT_ETHANE_1, SLCT_PROPANE_1,
                           PressInuse_1, TempInuse_1, USMAvgVOS_1
                    FROM {0}
                    WHERE LocalTimeCol >= @StartTime AND LocalTimeCol <= @EndTime
                    ORDER BY LocalTimeCol ASC
                    OFFSET @Offset ROWS FETCH NEXT @BatchSize ROWS ONLY";

                sql = string.Format(sql, flowMeterTag);
                
                int offset = 0;
                while (true)
                {
                    using var command = new SqlCommand(sql, connection);
                    command.CommandTimeout = DatabaseTimeoutSeconds;
                    command.Parameters.AddWithValue("@StartTime", targetTime.AddMinutes(-durationMinutes));
                    command.Parameters.AddWithValue("@EndTime", targetTime);
                    command.Parameters.AddWithValue("@Offset", offset);
                    command.Parameters.AddWithValue("@BatchSize", BATCH_SIZE);

                    using var reader = await command.ExecuteReaderAsync();
                    int recordCount = 0;

                    while (await reader.ReadAsync())
                    {
                        var row = new Dictionary<string, string>();
                        for (var i = 0; i < reader.FieldCount; i++)
                        {
                            row[reader.GetName(i)] = reader[i]?.ToString() ?? "";
                        }
                        reportData.Add(row);
                        recordCount++;
                    }

                    if (recordCount < BATCH_SIZE) break;
                    offset += BATCH_SIZE;

                    // 定期触发GC，释放内存
                    if (offset % 1000 == 0)
                    {
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                    }
                }
                
                stopwatch.Stop();
                _performanceMonitor?.RecordDatabaseOperation("GetReportData", stopwatch.Elapsed, reportData.Any());
                
                Log.Debug($"Retrieved {reportData.Count} report records for {flowMeterTag}");
                return reportData;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _performanceMonitor?.RecordDatabaseOperation("GetReportData", stopwatch.Elapsed, false);
                Log.Error($"Error getting report data for {flowMeterTag}: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 获取设备厂商信息
        /// </summary>
        public async Task<string> GetDeviceManufacturerAsync(string flowMeterTag, string stationTableName)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            await _connectionSemaphore.WaitAsync();
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                
                var sql = $@"
                    SELECT DeviceManufacturer 
                    FROM {stationTableName} 
                    WHERE FlowMeterTag = @FlowMeterTag";
                
                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@FlowMeterTag", flowMeterTag);
                
                var result = await command.ExecuteScalarAsync();
                
                stopwatch.Stop();
                _performanceMonitor?.RecordDatabaseOperation("GetDeviceManufacturer", stopwatch.Elapsed, result != null);
                
                return result?.ToString() ?? "";
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _performanceMonitor?.RecordDatabaseOperation("GetDeviceManufacturer", stopwatch.Elapsed, false);
                Log.Error($"Error getting device manufacturer for {flowMeterTag}: {ex.Message}", ex);
                return "";
            }
            finally
            {
                _connectionSemaphore.Release();
            }
        }

        #region IDisposable Implementation

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    Log.Info("Disposing OptimizedDatabaseService...");
                    _connectionSemaphore?.Dispose();
                    Log.Info("OptimizedDatabaseService disposed");
                }
                
                _disposed = true;
            }
        }

        #endregion
    }

    /// <summary>
    /// 设备数据容器
    /// </summary>
    public class DeviceData
    {
        public Dictionary<string, string> FCData { get; set; }
        public Dictionary<string, string> FTData { get; set; }
        public List<Dictionary<string, string>> ReportData { get; set; }
    }

    /// <summary>
    /// 设备组合
    /// </summary>
    public class DeviceCombination
    {
        public string Station { get; set; }
        public string FlowMeter { get; set; }
        public string FlowComputer { get; set; }
    }
}
