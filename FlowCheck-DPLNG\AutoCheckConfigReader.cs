using System;
using System.IO;
using System.Xml;

namespace FlowCheck_DPLNG
{
    /// <summary>
    /// 自动核查配置文件读取器
    /// </summary>
    public class AutoCheckConfigReader
    {
        private XmlDocument xmlDoc;
        private string configPath;

        public AutoCheckConfigReader(string xmlFilePath = "Config/AutoCheckConfig.xml")
        {
            configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, xmlFilePath);
            xmlDoc = new XmlDocument();
            
            // 如果配置文件不存在，创建默认配置
            if (!File.Exists(configPath))
            {
                CreateDefaultConfig();
            }
            
            xmlDoc.Load(configPath);
        }

        /// <summary>
        /// 创建默认配置文件
        /// </summary>
        private void CreateDefaultConfig()
        {
            var defaultConfig = @"<?xml version=""1.0"" encoding=""utf-8""?>
<AutoCheckConfiguration>
  <Schedule>
    <Hour>8</Hour>
    <Minute>0</Minute>
    <Enabled>true</Enabled>
  </Schedule>
  <ReportSettings>
    <OutputDirectory>C:\Reports\AutoCheck\</OutputDirectory>
    <FileNamePrefix>GUSM_SOSCHECK</FileNamePrefix>
  </ReportSettings>
  <LogSettings>
    <VerboseLogging>true</VerboseLogging>
    <KeepHistory>true</KeepHistory>
  </LogSettings>
</AutoCheckConfiguration>";

            Directory.CreateDirectory(Path.GetDirectoryName(configPath));
            File.WriteAllText(configPath, defaultConfig);
        }

        /// <summary>
        /// 获取XML节点值
        /// </summary>
        private string GetValue(string xpath)
        {
            XmlNode node = xmlDoc.SelectSingleNode(xpath);
            if (node != null)
            {
                return node.InnerText;
            }
            return string.Empty;
        }

        /// <summary>
        /// 获取整数配置值
        /// </summary>
        private int GetIntValue(string xpath, int defaultValue = 0)
        {
            string value = GetValue(xpath);
            if (int.TryParse(value, out int result))
            {
                return result;
            }
            return defaultValue;
        }

        /// <summary>
        /// 获取布尔配置值
        /// </summary>
        private bool GetBoolValue(string xpath, bool defaultValue = false)
        {
            string value = GetValue(xpath);
            if (bool.TryParse(value, out bool result))
            {
                return result;
            }
            return defaultValue;
        }

        /// <summary>
        /// 是否启用自动核查
        /// </summary>
        public bool IsAutoCheckEnabled()
        {
            return GetBoolValue("/AutoCheckConfiguration/Schedule/Enabled", true);
        }

        /// <summary>
        /// 获取执行小时数
        /// </summary>
        public int GetCheckHour()
        {
            int hour = GetIntValue("/AutoCheckConfiguration/Schedule/Hour", 8);
            return Math.Max(0, Math.Min(23, hour)); // 确保在0-23范围内
        }

        /// <summary>
        /// 获取执行分钟数
        /// </summary>
        public int GetCheckMinute()
        {
            int minute = GetIntValue("/AutoCheckConfiguration/Schedule/Minute", 0);
            return Math.Max(0, Math.Min(59, minute)); // 确保在0-59范围内
        }

        /// <summary>
        /// 获取报表输出目录
        /// </summary>
        public string GetReportOutputDirectory()
        {
            string directory = GetValue("/AutoCheckConfiguration/ReportSettings/OutputDirectory");
            if (string.IsNullOrEmpty(directory))
            {
                directory = @"C:\Reports\AutoCheck\";
            }
            return directory;
        }

        /// <summary>
        /// 获取文件名前缀
        /// </summary>
        public string GetFileNamePrefix()
        {
            string prefix = GetValue("/AutoCheckConfiguration/ReportSettings/FileNamePrefix");
            if (string.IsNullOrEmpty(prefix))
            {
                prefix = "GUSM_SOSCHECK";
            }
            return prefix;
        }

        /// <summary>
        /// 是否启用详细日志
        /// </summary>
        public bool IsVerboseLoggingEnabled()
        {
            return GetBoolValue("/AutoCheckConfiguration/LogSettings/VerboseLogging", true);
        }

        /// <summary>
        /// 是否保留处理历史
        /// </summary>
        public bool IsKeepHistoryEnabled()
        {
            return GetBoolValue("/AutoCheckConfiguration/LogSettings/KeepHistory", true);
        }

        /// <summary>
        /// 更新Schedule配置
        /// </summary>
        public void UpdateSchedule(int hour, int minute)
        {
            try
            {
                // 确保参数在有效范围内
                hour = Math.Max(0, Math.Min(23, hour));
                minute = Math.Max(0, Math.Min(59, minute));

                XmlNode hourNode = xmlDoc.SelectSingleNode("/AutoCheckConfiguration/Schedule/Hour");
                XmlNode minuteNode = xmlDoc.SelectSingleNode("/AutoCheckConfiguration/Schedule/Minute");

                if (hourNode != null)
                {
                    hourNode.InnerText = hour.ToString();
                }

                if (minuteNode != null)
                {
                    minuteNode.InnerText = minute.ToString();
                }

                // 保存XML文件
                xmlDoc.Save(configPath);
            }
            catch (Exception ex)
            {
                throw new Exception($"更新Schedule配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取完整的自动核查配置
        /// </summary>
        public AutoCheckConfig GetAutoCheckConfig()
        {
            return new AutoCheckConfig
            {
                IsEnabled = IsAutoCheckEnabled(),
                CheckHour = GetCheckHour(),
                CheckMinute = GetCheckMinute(),
                ReportOutputDirectory = GetReportOutputDirectory(),
                FileNamePrefix = GetFileNamePrefix(),
                VerboseLogging = IsVerboseLoggingEnabled(),
                KeepHistory = IsKeepHistoryEnabled()
            };
        }
    }

    /// <summary>
    /// 自动核查配置类
    /// </summary>
    public class AutoCheckConfig
    {
        public bool IsEnabled { get; set; }
        public int CheckHour { get; set; }
        public int CheckMinute { get; set; }
        public string ReportOutputDirectory { get; set; }
        public string FileNamePrefix { get; set; }
        public bool VerboseLogging { get; set; }
        public bool KeepHistory { get; set; }
    }
} 