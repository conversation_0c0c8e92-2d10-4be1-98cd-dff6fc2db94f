<?xml version="1.0" encoding="utf-8"?>
<PerformanceConfig>
  <!-- 性能优化配置 -->
  
  <!-- 并发控制设置 -->
  <ConcurrencySettings>
    <!-- 最大并发设备数 -->
    <MaxConcurrentDevices>20</MaxConcurrentDevices>
    
    <!-- 最大并发数据库连接数 -->
    <MaxConcurrentDatabaseConnections>10</MaxConcurrentDatabaseConnections>
    
    <!-- 最大并发Excel生成数 -->
    <MaxConcurrentExcelGeneration>5</MaxConcurrentExcelGeneration>
    
    <!-- 批处理大小 -->
    <BatchSize>10</BatchSize>
    
    <!-- 最大并发批次数 -->
    <MaxConcurrentBatches>3</MaxConcurrentBatches>
  </ConcurrencySettings>
  
  <!-- AGA10计算队列设置 -->
  <AGA10QueueSettings>
    <!-- 队列容量 -->
    <QueueCapacity>1000</QueueCapacity>
    
    <!-- 计算超时时间（分钟） -->
    <CalculationTimeoutMinutes>2</CalculationTimeoutMinutes>
    
    <!-- 最大重试次数 -->
    <MaxRetryAttempts>3</MaxRetryAttempts>
  </AGA10QueueSettings>
  
  <!-- 数据库优化设置 -->
  <DatabaseSettings>
    <!-- 查询超时时间（秒） -->
    <QueryTimeoutSeconds>30</QueryTimeoutSeconds>
    
    <!-- 批量查询大小 -->
    <BatchQuerySize>100</BatchQuerySize>
    
    <!-- 启用查询字段优化 -->
    <EnableFieldOptimization>true</EnableFieldOptimization>
    
    <!-- 启用流式处理 -->
    <EnableStreamProcessing>true</EnableStreamProcessing>
  </DatabaseSettings>
  
  <!-- 内存管理设置 -->
  <MemorySettings>
    <!-- 启用定期GC -->
    <EnablePeriodicGC>true</EnablePeriodicGC>
    
    <!-- GC触发间隔（处理的记录数） -->
    <GCTriggerInterval>1000</GCTriggerInterval>
    
    <!-- 最大内存使用量（MB） -->
    <MaxMemoryUsageMB>500</MaxMemoryUsageMB>
  </MemorySettings>
  
  <!-- 性能监控设置 -->
  <MonitoringSettings>
    <!-- 启用性能监控 -->
    <EnablePerformanceMonitoring>true</EnablePerformanceMonitoring>
    
    <!-- 报告间隔（分钟） -->
    <ReportingIntervalMinutes>1</ReportingIntervalMinutes>
    
    <!-- 启用详细日志 -->
    <EnableDetailedLogging>true</EnableDetailedLogging>
    
    <!-- 保留性能历史天数 -->
    <PerformanceHistoryDays>7</PerformanceHistoryDays>
  </MonitoringSettings>
  
  <!-- 优化开关 -->
  <OptimizationFlags>
    <!-- 启用AGA10计算队列 -->
    <EnableAGA10Queue>true</EnableAGA10Queue>
    
    <!-- 启用批量并行处理 -->
    <EnableBatchParallelProcessing>true</EnableBatchParallelProcessing>
    
    <!-- 启用数据库连接优化 -->
    <EnableDatabaseOptimization>true</EnableDatabaseOptimization>
    
    <!-- 启用Excel生成优化 -->
    <EnableExcelOptimization>true</EnableExcelOptimization>
    
    <!-- 启用内存优化 -->
    <EnableMemoryOptimization>true</EnableMemoryOptimization>
    
    <!-- 回退到原始实现（用于对比测试） -->
    <FallbackToOriginal>false</FallbackToOriginal>
  </OptimizationFlags>
  
  <!-- 性能阈值设置 -->
  <PerformanceThresholds>
    <!-- 设备处理超时阈值（秒） -->
    <DeviceProcessingTimeoutSeconds>300</DeviceProcessingTimeoutSeconds>
    
    <!-- 数据库查询超时阈值（秒） -->
    <DatabaseQueryTimeoutSeconds>60</DatabaseQueryTimeoutSeconds>
    
    <!-- AGA10计算超时阈值（秒） -->
    <AGA10CalculationTimeoutSeconds>30</AGA10CalculationTimeoutSeconds>
    
    <!-- Excel生成超时阈值（秒） -->
    <ExcelGenerationTimeoutSeconds>120</ExcelGenerationTimeoutSeconds>
    
    <!-- 最大失败率阈值（百分比） -->
    <MaxFailureRatePercent>10</MaxFailureRatePercent>
  </PerformanceThresholds>
  
  <!-- 调试和测试设置 -->
  <DebugSettings>
    <!-- 启用性能基准测试 -->
    <EnableBenchmarking>false</EnableBenchmarking>
    
    <!-- 测试设备数量 -->
    <TestDeviceCount>10</TestDeviceCount>
    
    <!-- 启用压力测试 -->
    <EnableStressTesting>false</EnableStressTesting>
    
    <!-- 模拟延迟（毫秒，用于测试） -->
    <SimulatedDelayMs>0</SimulatedDelayMs>
  </DebugSettings>
  
</PerformanceConfig>
