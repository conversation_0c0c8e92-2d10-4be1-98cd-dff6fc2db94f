﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using ScottPlot;
using ScottPlot.Plottable;

namespace FlowCheck_DPLNG
{
    public class DataPlotter
    {
        //private Dictionary<FormsPlot, Tooltip> tooltips = new Dictionary<FormsPlot, Tooltip>();

        public void PlotData(FormsPlot formsPlot, List<Dictionary<string, string>> data, double yLength,
            params string[] fieldTypes)
        {
            if (fieldTypes == null || fieldTypes.Length == 0)
            {
                throw new ArgumentException("At least one field type must be provided.");
            }

            var plt = formsPlot.Plot;
            plt.Clear();

            double minY = double.MaxValue;
            double maxY = double.MinValue;

            foreach (var fieldType in fieldTypes)
            {
                var fieldData = ExtractFieldData(data, fieldType);

                if (fieldData.Any())
                {
                    var sortedData = fieldData.OrderBy(d => d.time).ToList();
                    var xData = sortedData.Select(d => d.time.ToOADate()).ToArray();
                    var yData = sortedData.Select(d => d.value).ToArray();

                    minY = Math.Min(minY, yData.Min());
                    maxY = Math.Max(maxY, yData.Max());

                    var scatter = plt.AddScatter(xData, yData, label: fieldType);
                    scatter.LineWidth = 2;
                    scatter.MarkerSize = 5;
                }
            }

            ConfigurePlot(plt, minY, maxY, yLength);
            SetupTooltip(formsPlot);
            formsPlot.Render();
        }

        private List<(DateTime time, double value)> ExtractFieldData(List<Dictionary<string, string>> data,
            string fieldType)
        {
            var fieldData = new List<(DateTime time, double value)>();

            foreach (var row in data)
            {
                if (DateTime.TryParse(row["LocalTimeCol"], out DateTime time))
                {
                    var relevantFields = row.Keys.Where(k => k.Contains(fieldType));
                    foreach (var field in relevantFields)
                    {
                        if (double.TryParse(row[field], out double value))
                        {
                            fieldData.Add((time, value));
                        }
                    }
                }
            }

            return fieldData;
        }

        private void ConfigurePlot(Plot plt, double minY, double maxY, double yLength)
        {
            plt.Legend(enable: true, location: Alignment.UpperRight);
            plt.XAxis.DateTimeFormat(true);

            // 计算实际数据范围
            double dataRange = maxY - minY;

            // 计算Y轴的中心点
            double centerY = (minY + maxY) / 2;

            // 如果实际数据范围小于或等于指定的yLength，则使用yLength
            // 否则，使用实际数据范围，但增加一些边距
            double effectiveYLength = Math.Max(yLength, dataRange * 1.1); // 增加10%的边距

            // 设置Y轴范围
            double halfLength = effectiveYLength / 2;
            plt.SetAxisLimits(yMin: centerY - halfLength, yMax: centerY + halfLength);

            // 自动设置X轴范围
            plt.AxisAutoX();

            plt.Legend(true);
        }

        private void SetupTooltip(FormsPlot formsPlot)
        {
            // 移除旧的事件处理程序
            formsPlot.MouseMove -= FormsPlot_MouseMove;
            formsPlot.MouseLeave -= FormsPlot_MouseLeave;

            // 添加新的事件处理程序
            formsPlot.MouseMove += FormsPlot_MouseMove;
            formsPlot.MouseLeave += FormsPlot_MouseLeave;
        }

        //private void AddOrUpdateTooltip(FormsPlot formsPlot)
        //{
        //    if (!tooltips.ContainsKey(formsPlot))
        //    {
        //        var tooltip = formsPlot.Plot.AddTooltip("", 0, 0);
        //        tooltip.IsVisible = false;
        //        tooltips[formsPlot] = tooltip;
        //    }
        //    else
        //    {
        //        // 如果tooltip已存在，更新它的可见性
        //        tooltips[formsPlot].IsVisible = false;
        //    }

        //    // 移除现有的事件处理程序（如果有的话）
        //    formsPlot.MouseMove -= FormsPlot_MouseMove;
        //    formsPlot.MouseLeave -= FormsPlot_MouseLeave;

        //    // 重新添加事件处理程序
        //    formsPlot.MouseMove += FormsPlot_MouseMove;
        //    formsPlot.MouseLeave += FormsPlot_MouseLeave;
        //}

        private void FormsPlot_MouseMove(object sender, MouseEventArgs e)
        {
            if (sender is FormsPlot formsPlot)
            {
                var plt = formsPlot.Plot;
                var limits = plt.GetAxisLimits();
                (double mouseCoordX, double mouseCoordY) = formsPlot.GetMouseCoordinates();

                if (mouseCoordX < limits.XMin || mouseCoordX > limits.XMax ||
                    mouseCoordY < limits.YMin || mouseCoordY > limits.YMax)
                {
                    HideTooltip(formsPlot);
                    return;
                }

                DateTime mouseTime = DateTime.FromOADate(mouseCoordX);
                string tooltipText = $"Time: {mouseTime:yyyy-MM-dd HH:mm:ss}\n";

                bool foundPoint = false;
                foreach (var plottable in plt.GetPlottables())
                {
                    if (plottable is ScatterPlot scatterPlot)
                    {
                        var nearestPoint = scatterPlot.GetPointNearest(mouseCoordX, mouseCoordY);
                        if (nearestPoint.index >= 0 && Math.Abs(nearestPoint.x - mouseCoordX) < 0.5)
                        {
                            double value = scatterPlot.Ys[nearestPoint.index];
                            tooltipText += $"{scatterPlot.Label}: {value:F2}\n";
                            foundPoint = true;
                        }
                    }
                }

                if (foundPoint)
                {
                    ShowTooltip(formsPlot, tooltipText.TrimEnd('\n'), mouseCoordX, mouseCoordY);
                }
                else
                {
                    HideTooltip(formsPlot);
                }

                formsPlot.Render();
            }
        }


        private void FormsPlot_MouseLeave(object sender, EventArgs e)
        {
            if (sender is FormsPlot formsPlot)
            {
                HideTooltip(formsPlot);
                formsPlot.Render();
            }
        }

        private void ShowTooltip(FormsPlot formsPlot, string text, double x, double y)
        {
            var tooltip = formsPlot.Plot.GetPlottables().OfType<Tooltip>().FirstOrDefault();
            if (tooltip == null)
            {
                tooltip = formsPlot.Plot.AddTooltip(text, x, y);
            }
            else
            {
                tooltip.Label = text;
                tooltip.X = x;
                tooltip.Y = y;
            }

            tooltip.IsVisible = true;
        }

        private void HideTooltip(FormsPlot formsPlot)
        {
            var tooltip = formsPlot.Plot.GetPlottables().OfType<Tooltip>().FirstOrDefault();
            if (tooltip != null)
            {
                tooltip.IsVisible = false;
            }
        }
    }
}