﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using System.Timers;

namespace FlowCheck_DPLNG
{
    public class DatabasePoller : IDisposable
    {
        private Timer timer;
        private string connectionString;
        private string tableName;
        private string timeFieldName;
        private string[] keyWords;
        private bool isQuerying = false;

        public event EventHandler<Dictionary<string, object>> NewDataReceived;
        public event EventHandler<Exception> ErrorOccurred;

        public DatabasePoller(string connectionString, int intervalSeconds, string tableName, string timeFieldName,
            string[] keyWords)
        {
            this.connectionString = connectionString;
            this.tableName = tableName;
            this.timeFieldName = timeFieldName;
            this.keyWords = keyWords;

            timer = new Timer(intervalSeconds * 1000);
            timer.Elapsed += Timer_Elapsed;
        }

        public void Start()
        {
            timer.Start();
        }

        public void Stop()
        {
            timer.Stop();
        }

        private async void Timer_Elapsed(object sender, ElapsedEventArgs e)
        {
            if (isQuerying) return; // 防止重叠查询

            isQuerying = true;
            try
            {
                var latestRow = await FetchLatestRowAsync();
                if (latestRow != null && latestRow.Count > 0)
                {
                    NewDataReceived?.Invoke(this, latestRow);
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, ex);
            }
            finally
            {
                isQuerying = false;
            }
        }

        private async Task<Dictionary<string, object>> FetchLatestRowAsync()
        {
            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // 首先获取表的所有列名
                var columns = await GetTableColumnsAsync(connection);

                // 筛选出包含关键字的列名
                var filteredColumns = columns
                    .Where(c => keyWords.Any(k => c.IndexOf(k, StringComparison.OrdinalIgnoreCase) >= 0)).ToList();

                // 确保timeFieldName被包含在查询中
                if (!filteredColumns.Contains(timeFieldName))
                {
                    filteredColumns.Add(timeFieldName);
                }

                // 构建查询语句
                string columnList = string.Join(", ", filteredColumns);
                string query = $"SELECT TOP 1 {columnList} FROM {tableName} ORDER BY {timeFieldName} DESC";

                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            var row = new Dictionary<string, object>();
                            foreach (var column in filteredColumns)
                            {
                                row[column] = reader[column];
                            }

                            return row;
                        }
                    }
                }
            }

            return null;
        }

        private async Task<List<string>> GetTableColumnsAsync(SqlConnection connection)
        {
            var columns = new List<string>();
            string query = $"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{tableName}'";

            using (var command = new SqlCommand(query, connection))
            {
                using (var reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        columns.Add(reader.GetString(0));
                    }
                }
            }

            return columns;
        }

        public void Dispose()
        {
            timer?.Dispose();
        }
    }
}