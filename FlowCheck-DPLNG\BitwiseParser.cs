﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FlowCheck_DPLNG
{
    public class USMSystemStatusBitwiseParser
    {
        // 1. 定义 bit 位与其含义的映射关系
        // Key 是 bit 的位置 (0-31)，Value 是该 bit 为 true 时的字符串含义
        private static readonly Dictionary<int, string> BitMeanings = new Dictionary<int, string>
        {
            { 1, "软件不兼容" },
            { 2, "电源失败" },
            { 8, "可用声道数量太少" },
            { 9, "流速超限" },
            { 10, "检测到阻塞" },
            { 11, "IsBoreBuildupDetected" },
            { 12, "检测到液体" },
            { 13, "剖面系数异常" },
            { 14, "检测到反向流" }
            // ...可以根据需要添加更多定义，最多到 bit 31
        };

        /// <summary>
        /// 将一个整数按位解析，并根据预定义的含义生成描述字符串。
        /// 每个含义占一行。
        /// </summary>
        /// <param name="statusValue">包含状态信息的整数</param>
        /// <returns>一个多行字符串，描述了所有为 true 的 bit 位代表的含义</returns>
        public static string GetStatusDescriptionsFromInt(int statusValue)
        {
            // 如果输入为0，没有任何 bit 位为 true，直接返回空字符串
            if (statusValue == 0)
            {
                return string.Empty;
            }

            // 2. 创建一个列表，用于存放所有为 true 的 bit 位所代表的含义
            var activeDescriptions = new List<string>();

            // 3. 遍历一个 int 的所有 bit 位 (通常是32位)
            for (int i = 0; i < 32; i++)
            {
                // 4. 检查第 i 位是否为 1 (true)
                // (1 << i) 会创建一个只在第 i 位为1的掩码 (例如, i=2时, 掩码为 0100)
                // 使用按位与 (&) 运算符，如果结果不为0，说明 statusValue 的第 i 位也是1
                if ((statusValue & (1 << i)) != 0)
                {
                    // 5. 如果该 bit 位有预定义的含义，则将其添加到列表中
                    if (BitMeanings.TryGetValue(i, out string meaning))
                    {
                        activeDescriptions.Add(meaning);
                    }
                    // 可选：如果想显示未定义的 bit 位，可以加一个 else
                    // else
                    // {
                    //     activeDescriptions.Add($"未定义的标志位: {i}");
                    // }
                }
            }

            // 6. 将列表中的所有字符串用换行符连接成一个最终的字符串
            // Environment.NewLine 是跨平台的换行符，比 "\n" 更推荐
            return string.Join(Environment.NewLine, activeDescriptions);
        }
    }
}