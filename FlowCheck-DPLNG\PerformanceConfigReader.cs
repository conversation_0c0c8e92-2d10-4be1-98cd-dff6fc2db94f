using System;
using System.IO;
using System.Xml;
using log4net;

namespace FlowCheck_DPLNG
{
    /// <summary>
    /// 性能配置读取器
    /// </summary>
    public class PerformanceConfigReader
    {
        private static readonly ILog Log = LogManager.GetLogger(typeof(PerformanceConfigReader));
        
        private readonly string _configFilePath;
        private PerformanceConfig _config;

        public PerformanceConfigReader(string configFilePath = "Config/PerformanceConfig.xml")
        {
            _configFilePath = configFilePath;
            LoadConfiguration();
        }

        /// <summary>
        /// 获取性能配置
        /// </summary>
        public PerformanceConfig GetConfig()
        {
            return _config ?? new PerformanceConfig();
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public void ReloadConfiguration()
        {
            LoadConfiguration();
        }

        /// <summary>
        /// 加载配置文件
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    Log.Warn($"Performance config file not found: {_configFilePath}, using default settings");
                    _config = new PerformanceConfig();
                    return;
                }

                var doc = new XmlDocument();
                doc.Load(_configFilePath);

                _config = new PerformanceConfig
                {
                    // 并发控制设置
                    MaxConcurrentDevices = GetIntValue(doc, "//ConcurrencySettings/MaxConcurrentDevices", 20),
                    MaxConcurrentDatabaseConnections = GetIntValue(doc, "//ConcurrencySettings/MaxConcurrentDatabaseConnections", 10),
                    MaxConcurrentExcelGeneration = GetIntValue(doc, "//ConcurrencySettings/MaxConcurrentExcelGeneration", 5),
                    BatchSize = GetIntValue(doc, "//ConcurrencySettings/BatchSize", 10),
                    MaxConcurrentBatches = GetIntValue(doc, "//ConcurrencySettings/MaxConcurrentBatches", 3),

                    // AGA10队列设置
                    QueueCapacity = GetIntValue(doc, "//AGA10QueueSettings/QueueCapacity", 1000),
                    CalculationTimeoutMinutes = GetIntValue(doc, "//AGA10QueueSettings/CalculationTimeoutMinutes", 2),
                    MaxRetryAttempts = GetIntValue(doc, "//AGA10QueueSettings/MaxRetryAttempts", 3),

                    // 数据库设置
                    QueryTimeoutSeconds = GetIntValue(doc, "//DatabaseSettings/QueryTimeoutSeconds", 30),
                    BatchQuerySize = GetIntValue(doc, "//DatabaseSettings/BatchQuerySize", 100),
                    EnableFieldOptimization = GetBoolValue(doc, "//DatabaseSettings/EnableFieldOptimization", true),
                    EnableStreamProcessing = GetBoolValue(doc, "//DatabaseSettings/EnableStreamProcessing", true),

                    // 内存管理设置
                    EnablePeriodicGC = GetBoolValue(doc, "//MemorySettings/EnablePeriodicGC", true),
                    GCTriggerInterval = GetIntValue(doc, "//MemorySettings/GCTriggerInterval", 1000),
                    MaxMemoryUsageMB = GetIntValue(doc, "//MemorySettings/MaxMemoryUsageMB", 500),

                    // 监控设置
                    EnablePerformanceMonitoring = GetBoolValue(doc, "//MonitoringSettings/EnablePerformanceMonitoring", true),
                    ReportingIntervalMinutes = GetIntValue(doc, "//MonitoringSettings/ReportingIntervalMinutes", 1),
                    EnableDetailedLogging = GetBoolValue(doc, "//MonitoringSettings/EnableDetailedLogging", true),
                    PerformanceHistoryDays = GetIntValue(doc, "//MonitoringSettings/PerformanceHistoryDays", 7),

                    // 优化开关
                    EnableAGA10Queue = GetBoolValue(doc, "//OptimizationFlags/EnableAGA10Queue", true),
                    EnableBatchParallelProcessing = GetBoolValue(doc, "//OptimizationFlags/EnableBatchParallelProcessing", true),
                    EnableDatabaseOptimization = GetBoolValue(doc, "//OptimizationFlags/EnableDatabaseOptimization", true),
                    EnableExcelOptimization = GetBoolValue(doc, "//OptimizationFlags/EnableExcelOptimization", true),
                    EnableMemoryOptimization = GetBoolValue(doc, "//OptimizationFlags/EnableMemoryOptimization", true),
                    FallbackToOriginal = GetBoolValue(doc, "//OptimizationFlags/FallbackToOriginal", false),

                    // 性能阈值
                    DeviceProcessingTimeoutSeconds = GetIntValue(doc, "//PerformanceThresholds/DeviceProcessingTimeoutSeconds", 300),
                    DatabaseQueryTimeoutSeconds = GetIntValue(doc, "//PerformanceThresholds/DatabaseQueryTimeoutSeconds", 60),
                    AGA10CalculationTimeoutSeconds = GetIntValue(doc, "//PerformanceThresholds/AGA10CalculationTimeoutSeconds", 30),
                    ExcelGenerationTimeoutSeconds = GetIntValue(doc, "//PerformanceThresholds/ExcelGenerationTimeoutSeconds", 120),
                    MaxFailureRatePercent = GetIntValue(doc, "//PerformanceThresholds/MaxFailureRatePercent", 10),

                    // 调试设置
                    EnableBenchmarking = GetBoolValue(doc, "//DebugSettings/EnableBenchmarking", false),
                    TestDeviceCount = GetIntValue(doc, "//DebugSettings/TestDeviceCount", 10),
                    EnableStressTesting = GetBoolValue(doc, "//DebugSettings/EnableStressTesting", false),
                    SimulatedDelayMs = GetIntValue(doc, "//DebugSettings/SimulatedDelayMs", 0)
                };

                Log.Info($"Performance configuration loaded successfully from: {_configFilePath}");
                LogConfigurationSummary();
            }
            catch (Exception ex)
            {
                Log.Error($"Error loading performance configuration: {ex.Message}", ex);
                _config = new PerformanceConfig();
            }
        }

        /// <summary>
        /// 获取整数值
        /// </summary>
        private int GetIntValue(XmlDocument doc, string xpath, int defaultValue)
        {
            try
            {
                var node = doc.SelectSingleNode(xpath);
                if (node != null && int.TryParse(node.InnerText, out int value))
                {
                    return value;
                }
            }
            catch (Exception ex)
            {
                Log.Debug($"Error reading int value from {xpath}: {ex.Message}");
            }
            return defaultValue;
        }

        /// <summary>
        /// 获取布尔值
        /// </summary>
        private bool GetBoolValue(XmlDocument doc, string xpath, bool defaultValue)
        {
            try
            {
                var node = doc.SelectSingleNode(xpath);
                if (node != null && bool.TryParse(node.InnerText, out bool value))
                {
                    return value;
                }
            }
            catch (Exception ex)
            {
                Log.Debug($"Error reading bool value from {xpath}: {ex.Message}");
            }
            return defaultValue;
        }

        /// <summary>
        /// 记录配置摘要
        /// </summary>
        private void LogConfigurationSummary()
        {
            Log.Info("=== Performance Configuration Summary ===");
            Log.Info($"Max Concurrent Devices: {_config.MaxConcurrentDevices}");
            Log.Info($"Max Concurrent DB Connections: {_config.MaxConcurrentDatabaseConnections}");
            Log.Info($"Batch Size: {_config.BatchSize}");
            Log.Info($"AGA10 Queue Capacity: {_config.QueueCapacity}");
            Log.Info($"Enable AGA10 Queue: {_config.EnableAGA10Queue}");
            Log.Info($"Enable Batch Parallel Processing: {_config.EnableBatchParallelProcessing}");
            Log.Info($"Enable Database Optimization: {_config.EnableDatabaseOptimization}");
            Log.Info($"Fallback to Original: {_config.FallbackToOriginal}");
        }
    }

    /// <summary>
    /// 性能配置类
    /// </summary>
    public class PerformanceConfig
    {
        // 并发控制设置
        public int MaxConcurrentDevices { get; set; } = 20;
        public int MaxConcurrentDatabaseConnections { get; set; } = 10;
        public int MaxConcurrentExcelGeneration { get; set; } = 5;
        public int BatchSize { get; set; } = 10;
        public int MaxConcurrentBatches { get; set; } = 3;

        // AGA10队列设置
        public int QueueCapacity { get; set; } = 1000;
        public int CalculationTimeoutMinutes { get; set; } = 2;
        public int MaxRetryAttempts { get; set; } = 3;

        // 数据库设置
        public int QueryTimeoutSeconds { get; set; } = 30;
        public int BatchQuerySize { get; set; } = 100;
        public bool EnableFieldOptimization { get; set; } = true;
        public bool EnableStreamProcessing { get; set; } = true;

        // 内存管理设置
        public bool EnablePeriodicGC { get; set; } = true;
        public int GCTriggerInterval { get; set; } = 1000;
        public int MaxMemoryUsageMB { get; set; } = 500;

        // 监控设置
        public bool EnablePerformanceMonitoring { get; set; } = true;
        public int ReportingIntervalMinutes { get; set; } = 1;
        public bool EnableDetailedLogging { get; set; } = true;
        public int PerformanceHistoryDays { get; set; } = 7;

        // 优化开关
        public bool EnableAGA10Queue { get; set; } = true;
        public bool EnableBatchParallelProcessing { get; set; } = true;
        public bool EnableDatabaseOptimization { get; set; } = true;
        public bool EnableExcelOptimization { get; set; } = true;
        public bool EnableMemoryOptimization { get; set; } = true;
        public bool FallbackToOriginal { get; set; } = false;

        // 性能阈值
        public int DeviceProcessingTimeoutSeconds { get; set; } = 300;
        public int DatabaseQueryTimeoutSeconds { get; set; } = 60;
        public int AGA10CalculationTimeoutSeconds { get; set; } = 30;
        public int ExcelGenerationTimeoutSeconds { get; set; } = 120;
        public int MaxFailureRatePercent { get; set; } = 10;

        // 调试设置
        public bool EnableBenchmarking { get; set; } = false;
        public int TestDeviceCount { get; set; } = 10;
        public bool EnableStressTesting { get; set; } = false;
        public int SimulatedDelayMs { get; set; } = 0;
    }
}
