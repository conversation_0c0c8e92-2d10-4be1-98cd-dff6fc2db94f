﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="EPPlus" version="5.7.4" targetFramework="net461" />
  <package id="HarfBuzzSharp" version="7.3.0.3" targetFramework="net48" />
  <package id="HarfBuzzSharp.NativeAssets.Linux" version="7.3.0.3" targetFramework="net48" />
  <package id="HarfBuzzSharp.NativeAssets.macOS" version="7.3.0.3" targetFramework="net48" />
  <package id="HarfBuzzSharp.NativeAssets.Win32" version="7.3.0.3" targetFramework="net48" />
  <package id="HslCommunication" version="12.1.3" targetFramework="net48" />
  <package id="LiveChartsCore" version="2.0.0-rc4.5" targetFramework="net48" />
  <package id="LiveChartsCore.SkiaSharpView" version="2.0.0-rc4.5" targetFramework="net48" />
  <package id="log4net" version="2.0.17" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net48" />
  <package id="Microsoft.IO.RecyclableMemoryStream" version="1.4.1" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net48" />
  <package id="OpenTK" version="3.1.0" targetFramework="net48" />
  <package id="OpenTK.GLControl" version="3.1.0" targetFramework="net48" />
  <package id="ScottPlot" version="4.1.74" targetFramework="net48" />
  <package id="ScottPlot.WinForms" version="4.1.74" targetFramework="net48" />
  <package id="SkiaSharp" version="3.118.0-preview.1.2" targetFramework="net48" />
  <package id="SkiaSharp.HarfBuzz" version="2.88.9" targetFramework="net48" />
  <package id="SkiaSharp.NativeAssets.Linux.NoDependencies" version="2.88.9" targetFramework="net48" />
  <package id="SkiaSharp.NativeAssets.macOS" version="3.118.0-preview.1.2" targetFramework="net48" />
  <package id="SkiaSharp.NativeAssets.Win32" version="3.118.0-preview.1.2" targetFramework="net48" />
  <package id="SkiaSharp.Views.Desktop.Common" version="3.118.0-preview.1.2" targetFramework="net48" />
  <package id="SkiaSharp.Views.WindowsForms" version="3.118.0-preview.1.2" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.ComponentModel.Annotations" version="4.7.0" targetFramework="net461" />
  <package id="System.Drawing.Common" version="4.7.2" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="8.0.0" targetFramework="net48" />
  <package id="System.Text.Json" version="8.0.5" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
</packages>