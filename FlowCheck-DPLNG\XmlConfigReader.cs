﻿using System;
using System.IO;
using System.Xml;

namespace FlowCheck_DPLNG
{
    public class XmlConfigReader
    {
        private XmlDocument xmlDoc;
        private string configPath;

        public XmlConfigReader(string xmlFilePath)
        {
            configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, xmlFilePath);
            xmlDoc = new XmlDocument();
            xmlDoc.Load(configPath);
        }

        public string GetValue(string xpath)
        {
            XmlNode node = xmlDoc.SelectSingleNode(xpath);
            if (node != null)
            {
                return node.InnerText;
            }

            return string.Empty;
        }

        public string GetConnectionString()
        {
            string server = GetValue("/Configuration/DatabaseConnection/Server");
            string database = GetValue("/Configuration/DatabaseConnection/Database");
            string username = GetValue("/Configuration/DatabaseConnection/Username");
            string password = GetValue("/Configuration/DatabaseConnection/Password");

            return $"Data Source={server};Initial Catalog={database};User Id={username};Password={password};";
        }

        public string GetReportPath()
        {
            return GetValue("/Configuration/ReportSettings/ReportPath");
        }

        public string GetStationDataTableName()
        {
            return GetValue("/Configuration/StationDataTable/TableName");
        }
    }
}