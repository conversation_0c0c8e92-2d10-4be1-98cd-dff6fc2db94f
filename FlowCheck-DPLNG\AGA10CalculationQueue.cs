using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Runtime.InteropServices;
using log4net;

namespace FlowCheck_DPLNG
{
    /// <summary>
    /// AGA10计算队列 - 解决234dll.dll线程安全问题的高性能方案
    /// 使用生产者-消费者模式，专用线程串行处理DLL调用，其他线程非阻塞提交请求
    /// </summary>
    public class AGA10CalculationQueue : IDisposable
    {
        private static readonly ILog Log = LogManager.GetLogger(typeof(AGA10CalculationQueue));
        
        private readonly ConcurrentQueue<AGA10CalculationRequest> _requestQueue;
        private readonly SemaphoreSlim _queueSemaphore;
        private readonly Task _processingTask;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly Dictionary<string, string> _fieldMappings;
        private readonly string _historyTagSuffix;
        private readonly int _queueCapacity;
        private bool _disposed = false;

        // AGA10算法DLL导入 - 保持与原代码完全一致
        [DllImport(@"Lib\234dll.dll", EntryPoint = "?AGA10_Init@@YGHXZ", ExactSpelling = true, CharSet = CharSet.Ansi, SetLastError = true)]
        private static extern long AGA10_Init();
        
        [DllImport(@"Lib\234dll.dll", EntryPoint = "?AGA10_UnInit@@YGHXZ", ExactSpelling = true, CharSet = CharSet.Ansi, SetLastError = true)]
        private static extern long AGA10_UnInit();
        
        [DllImport(@"Lib\234dll.dll", EntryPoint = "?Crit@@YGNPAUtagAGA10STRUCT@@N@Z", CallingConvention = CallingConvention.StdCall)]
        private static extern double Crit(ref AGA10STRUCT AGAPtr, double dPlenumVelocity);

        public AGA10CalculationQueue(int queueCapacity = 1000)
        {
            _queueCapacity = queueCapacity;
            _requestQueue = new ConcurrentQueue<AGA10CalculationRequest>();
            _queueSemaphore = new SemaphoreSlim(0); // 初始为0，表示队列为空
            _cancellationTokenSource = new CancellationTokenSource();

            // 初始化字段映射 - 支持RMG和DANIEL设备
            _fieldMappings = new Dictionary<string, string>();
            _historyTagSuffix = "_1";

            // 启动专用处理线程
            _processingTask = ProcessCalculationsAsync(_cancellationTokenSource.Token);

            Log.Info("AGA10CalculationQueue initialized with queue capacity: " + queueCapacity);
        }

        /// <summary>
        /// 异步提交AGA10计算请求（非阻塞）
        /// </summary>
        public async Task<CalculationResult> CalculateAsync(
            Dictionary<string, string> data, 
            string flowMeterTag, 
            bool isRMGDevice,
            TimeSpan? timeout = null)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(AGA10CalculationQueue));

            var request = new AGA10CalculationRequest
            {
                Data = data,
                FlowMeterTag = flowMeterTag,
                IsRMGDevice = isRMGDevice,
                CompletionSource = new TaskCompletionSource<CalculationResult>(),
                RequestTime = DateTime.Now,
                RequestId = Guid.NewGuid()
            };
            
            Log.Debug($"Submitting AGA10 calculation request: {request}");
            
            try
            {
                // 检查队列容量
                if (_requestQueue.Count >= _queueCapacity)
                {
                    Log.Warn($"AGA10 calculation queue is full, capacity: {_queueCapacity}");
                    throw new InvalidOperationException("AGA10 calculation queue is full");
                }

                // 将请求放入队列
                _requestQueue.Enqueue(request);
                _queueSemaphore.Release(); // 通知处理线程有新请求

                // 异步等待结果，线程可以去做其他工作
                var timeoutTask = timeout.HasValue ?
                    Task.Delay(timeout.Value, _cancellationTokenSource.Token) :
                    Task.Delay(Timeout.Infinite, _cancellationTokenSource.Token);

                var completedTask = await Task.WhenAny(request.CompletionSource.Task, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    Log.Error($"AGA10 calculation timeout for request: {request}");
                    throw new TimeoutException($"AGA10 calculation timeout for {flowMeterTag}");
                }

                return await request.CompletionSource.Task;
            }
            catch (OperationCanceledException)
            {
                Log.Warn($"AGA10 calculation cancelled for request: {request}");
                throw;
            }
        }

        /// <summary>
        /// 专用线程处理AGA10计算请求
        /// </summary>
        private async Task ProcessCalculationsAsync(CancellationToken cancellationToken)
        {
            Log.Info("AGA10 calculation processing thread started");

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        // 等待队列中有请求
                        await _queueSemaphore.WaitAsync(cancellationToken);

                        // 尝试从队列中取出请求
                        if (_requestQueue.TryDequeue(out AGA10CalculationRequest request))
                        {
                            Log.Debug($"Processing AGA10 calculation request: {request}");

                            // 在专用线程中串行调用DLL，无需锁
                            var result = ExecuteAGA10CalculationInternal(
                                request.Data,
                                request.FlowMeterTag,
                                request.IsRMGDevice
                            );

                            request.CompletionSource.SetResult(result);

                            Log.Debug($"AGA10 calculation completed for request: {request}");
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        // 正常取消，退出循环
                        break;
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"Error processing AGA10 calculation request: {ex.Message}", ex);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                Log.Info("AGA10 calculation processing thread cancelled");
            }
            catch (Exception ex)
            {
                Log.Error($"AGA10 calculation processing thread error: {ex.Message}", ex);
            }
            finally
            {
                Log.Info("AGA10 calculation processing thread stopped");
            }
        }

        /// <summary>
        /// 执行AGA10计算的内部实现 - 完全复制原有逻辑，保持DLL调用方法不变
        /// </summary>
        private CalculationResult ExecuteAGA10CalculationInternal(
            Dictionary<string, string> data, 
            string flowMeterTag, 
            bool isRMGDevice)
        {
            const int MAX_RETRY_ATTEMPTS = 3;
            Exception lastException = null;

            // 初始化字段映射
            InitializeFieldMappings(isRMGDevice);

            for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++)
            {
                try
                {
                    Log.Debug($"AGA10 calculation attempt {attempt}/{MAX_RETRY_ATTEMPTS} for {flowMeterTag}");

                    // ===== 第一步：DLL初始化 - 保持原有调用方式 =====
                    var initResult = AGA10_Init();
                    Log.Debug($"AGA10_Init result code: {initResult}");
                    
                    if (initResult != 9001)
                    {
                        throw new Exception($"AGA10_Init failed, expected 9001, actual: {initResult}");
                    }

                    // ===== 第二步：构建AGA10结构体 - 保持原有逻辑 =====
                    var aga10Struct = BuildAGA10Struct(data, isRMGDevice);

                    // ===== 第三步：执行计算 - 保持原有调用方式 =====
                    var calculatedSOS = Crit(ref aga10Struct, 0.0);

                    // ===== 第四步：验证结果 =====
                    if (double.IsNaN(calculatedSOS) || double.IsInfinity(calculatedSOS) || calculatedSOS <= 0)
                    {
                        throw new Exception($"Invalid calculation result: {calculatedSOS}");
                    }

                    // ===== 第五步：DLL清理 - 保持原有调用方式 =====
                    var uninitResult = AGA10_UnInit();
                    if (uninitResult != 0)
                    {
                        Log.Warn($"AGA10_UnInit returned code: {uninitResult}");
                    }

                    // ===== 第六步：获取测量声速和计算结果 =====
                    var measuredSOS = GetMeasuredSOS(data, isRMGDevice);

                    var result = new CalculationResult
                    {
                        CalculatedSOS = calculatedSOS,
                        MeasuredSOS = measuredSOS,
                        Deviation = Math.Abs(calculatedSOS - measuredSOS),
                        IsValid = true,
                        CalculationTime = DateTime.Now
                    };

                    Log.Info($"AGA10 calculation successful for {flowMeterTag}: " +
                            $"Calculated={calculatedSOS:F4}, Measured={measuredSOS:F4}, " +
                            $"Deviation={result.Deviation:F4}");
                    
                    return result;
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    Log.Warn($"AGA10 calculation attempt {attempt} failed for {flowMeterTag}: {ex.Message}");

                    // 确保DLL清理
                    try { AGA10_UnInit(); } catch { }

                    if (attempt < MAX_RETRY_ATTEMPTS)
                    {
                        // 短暂延迟后重试
                        Thread.Sleep(100 * attempt);
                    }
                }
            }

            Log.Error($"AGA10 calculation failed for {flowMeterTag} after {MAX_RETRY_ATTEMPTS} attempts: {lastException?.Message}", lastException);
            return null;
        }

        /// <summary>
        /// 初始化字段映射 - 复制原有逻辑
        /// </summary>
        private void InitializeFieldMappings(bool isRMGDevice)
        {
            _fieldMappings.Clear();
            
            if (isRMGDevice)
            {
                // RMG设备字段映射
                _fieldMappings["SLCT_METHANE"] = "SLCT_METHANE" + _historyTagSuffix;
                _fieldMappings["SLCT_ETHANE"] = "SLCT_ETHANE" + _historyTagSuffix;
                _fieldMappings["SLCT_PROPANE"] = "SLCT_PROPANE" + _historyTagSuffix;
                _fieldMappings["SLCT_NITROGEN"] = "SLCT_NITROGEN" + _historyTagSuffix;
                _fieldMappings["SLCT_CO2"] = "SLCT_CO2" + _historyTagSuffix;
                _fieldMappings["PressInuse"] = "PressInuse" + _historyTagSuffix;
                _fieldMappings["TempInuse"] = "TempInuse" + _historyTagSuffix;
                _fieldMappings["USMAvgVOS"] = "USMAvgVOS" + _historyTagSuffix;
            }
            else
            {
                // DANIEL设备字段映射
                _fieldMappings["SLCT_METHANE"] = "SLCT_METHANE" + _historyTagSuffix;
                _fieldMappings["SLCT_ETHANE"] = "SLCT_ETHANE" + _historyTagSuffix;
                _fieldMappings["SLCT_PROPANE"] = "SLCT_PROPANE" + _historyTagSuffix;
                _fieldMappings["SLCT_NITROGEN"] = "SLCT_NITROGEN" + _historyTagSuffix;
                _fieldMappings["SLCT_CO2"] = "SLCT_CO2" + _historyTagSuffix;
                _fieldMappings["PressInuse"] = "PressInuse" + _historyTagSuffix;
                _fieldMappings["TempInuse"] = "TempInuse" + _historyTagSuffix;
                _fieldMappings["USMAvgVOS"] = "USMAvgVOS" + _historyTagSuffix;
            }
        }

        /// <summary>
        /// 构建AGA10结构体 - 完全复制原有逻辑
        /// </summary>
        private AGA10STRUCT BuildAGA10Struct(Dictionary<string, string> data, bool isRMGDevice)
        {
            var aga10Struct = new AGA10STRUCT();

            try
            {
                // 组分数据 - 保持原有的解析逻辑
                aga10Struct.Methane = ParseDoubleOrDefault(data, _fieldMappings["SLCT_METHANE"]);
                aga10Struct.Ethane = ParseDoubleOrDefault(data, _fieldMappings["SLCT_ETHANE"]);
                aga10Struct.Propane = ParseDoubleOrDefault(data, _fieldMappings["SLCT_PROPANE"]);
                aga10Struct.Nitrogen = ParseDoubleOrDefault(data, _fieldMappings["SLCT_NITROGEN"]);
                aga10Struct.CO2 = ParseDoubleOrDefault(data, _fieldMappings["SLCT_CO2"]);

                // 其他组分设为0 - 保持原有逻辑
                aga10Struct.H2O = 0;
                aga10Struct.H2S = 0;
                aga10Struct.H2 = 0;
                aga10Struct.CO = 0;
                aga10Struct.O2 = 0;
                aga10Struct.i_Butane = 0;
                aga10Struct.n_Butane = 0;
                aga10Struct.i_Pentane = 0;
                aga10Struct.n_Pentane = 0;
                aga10Struct.n_Hexane = 0;
                aga10Struct.n_Heptane = 0;
                aga10Struct.n_Octane = 0;
                aga10Struct.n_Nonane = 0;
                aga10Struct.n_Decane = 0;
                aga10Struct.He = 0;
                aga10Struct.Ar = 0;

                // 压力和温度 - 保持原有的单位转换逻辑
                var pressureKPa = ParseDoubleOrDefault(data, _fieldMappings["PressInuse"]);
                var temperatureCelsius = ParseDoubleOrDefault(data, _fieldMappings["TempInuse"]);

                aga10Struct.dPf = pressureKPa * 1000; // kPa转Pa
                aga10Struct.dTf = temperatureCelsius + 273.15; // 摄氏度转开尔文

                // 标准条件 - 保持原有设置
                aga10Struct.dPb = 101325; // 标准大气压 Pa
                aga10Struct.dTb = 288.15;  // 标准温度 15°C = 288.15K

                // 状态标志 - 保持原有设置
                aga10Struct.lStatus = 0;
                aga10Struct.bForceUpdate = 1;

                Log.Debug($"AGA10 struct built - Methane: {aga10Struct.Methane:F2}%, " +
                         $"Pressure: {pressureKPa:F2} kPa, Temperature: {temperatureCelsius:F2}°C");

                return aga10Struct;
            }
            catch (Exception ex)
            {
                Log.Error($"Error building AGA10 struct: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 获取测量声速 - 复制原有逻辑
        /// </summary>
        private double GetMeasuredSOS(Dictionary<string, string> data, bool isRMGDevice)
        {
            try
            {
                var measuredSOS = ParseDoubleOrDefault(data, _fieldMappings["USMAvgVOS"]);
                Log.Debug($"Measured SOS: {measuredSOS:F4} m/s");
                return measuredSOS;
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting measured SOS: {ex.Message}", ex);
                return 0.0;
            }
        }

        /// <summary>
        /// 解析双精度数值 - 复制原有逻辑
        /// </summary>
        private double ParseDoubleOrDefault(Dictionary<string, string> data, string key)
        {
            if (data.TryGetValue(key, out string value) &&
                double.TryParse(value, out double result))
            {
                return result;
            }

            Log.Warn($"Failed to parse double value for key: {key}, value: {value ?? "null"}");
            return 0.0;
        }

        /// <summary>
        /// 获取队列统计信息
        /// </summary>
        public QueueStatistics GetStatistics()
        {
            return new QueueStatistics
            {
                QueueLength = _requestQueue.Count,
                IsCompleted = _processingTask?.IsCompleted ?? true,
                ProcessingThreadAlive = _processingTask?.Status == TaskStatus.Running
            };
        }

        #region IDisposable Implementation

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    Log.Info("Disposing AGA10CalculationQueue...");

                    // 取消处理线程
                    _cancellationTokenSource.Cancel();

                    try
                    {
                        // 等待处理线程完成
                        _processingTask?.Wait(TimeSpan.FromSeconds(5));
                    }
                    catch (Exception ex)
                    {
                        Log.Warn($"Error waiting for processing task completion: {ex.Message}");
                    }

                    _cancellationTokenSource?.Dispose();
                    _queueSemaphore?.Dispose();

                    Log.Info("AGA10CalculationQueue disposed");
                }

                _disposed = true;
            }
        }

        #endregion
    }

    /// <summary>
    /// AGA10计算请求
    /// </summary>
    public class AGA10CalculationRequest
    {
        public Dictionary<string, string> Data { get; set; }
        public string FlowMeterTag { get; set; }
        public bool IsRMGDevice { get; set; }
        public TaskCompletionSource<CalculationResult> CompletionSource { get; set; }
        public DateTime RequestTime { get; set; } = DateTime.Now;
        public int Priority { get; set; } = 0;
        public Guid RequestId { get; set; } = Guid.NewGuid();

        public override string ToString()
        {
            return $"AGA10Request[{RequestId:N}] - {FlowMeterTag} (RMG: {IsRMGDevice}, Priority: {Priority})";
        }
    }

    /// <summary>
    /// 计算结果
    /// </summary>
    public class CalculationResult
    {
        public double CalculatedSOS { get; set; }
        public double MeasuredSOS { get; set; }
        public double Deviation { get; set; }
        public bool IsValid { get; set; }
        public DateTime CalculationTime { get; set; }
    }

    /// <summary>
    /// 队列统计信息
    /// </summary>
    public class QueueStatistics
    {
        public int QueueLength { get; set; }
        public bool IsCompleted { get; set; }
        public bool ProcessingThreadAlive { get; set; }
    }

    /// <summary>
    /// AGA10算法结构体 - 保持与原代码完全一致
    /// </summary>
    public struct AGA10STRUCT
    {
        public int lStatus;
        public int bForceUpdate;
        public double Methane;
        public double Nitrogen;
        public double CO2;
        public double Ethane;
        public double Propane;
        public double H2O;
        public double H2S;
        public double H2;
        public double CO;
        public double O2;
        public double i_Butane;
        public double n_Butane;
        public double i_Pentane;
        public double n_Pentane;
        public double n_Hexane;
        public double n_Heptane;
        public double n_Octane;
        public double n_Nonane;
        public double n_Decane;
        public double He;
        public double Ar;
        public double dPb;
        public double dTb;
        public double dPf;
        public double dTf;
    }
}
