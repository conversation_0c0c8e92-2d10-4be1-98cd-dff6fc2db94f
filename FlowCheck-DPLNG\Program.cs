﻿using log4net.Config;
using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

[assembly: log4net.Config.XmlConfigurator(ConfigFile = "log4net.config", Watch = true)]

namespace FlowCheck_DPLNG
{
    internal static class Program
    {
        // Win32 API 声明
        [DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int cmdShow);

        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern int RegisterWindowMessage(string lpString);

        [DllImport("user32.dll")]
        private static extern bool PostMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);

        private const int HWND_BROADCAST = 0xffff;

        // ShowWindow 命令常量
        private const int SW_RESTORE = 9;

        // 定义互斥体名称
        private static string mutexName = "FlowCheck_DPLNG_E1ARRR56389AS3-9FG6-4C1F-8982-68852OLK67FBHJ7E23";

        // 定义自定义Windows消息
        private static readonly string SHOW_WINDOW_MESSAGE = "FlowCheck_DPLNG_ShowWindow_Message";
        private static int showWindowMessage;

        private static Mutex mutex;
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        private static void Main()
        {
            try
            {
                // 配置 log4net
                var logRepository = log4net.LogManager.GetRepository(Assembly.GetEntryAssembly());
                XmlConfigurator.Configure(logRepository, new FileInfo("log4net.config"));

                // 启用行号记录（如果需要的话）
                log4net.GlobalContext.Properties["log4net.HostName"] = "";

                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);


                // 注册自定义Windows消息
                showWindowMessage = RegisterWindowMessage(SHOW_WINDOW_MESSAGE);

                // 尝试创建命名互斥体
                mutex = new Mutex(true, mutexName, out bool createdNew);

                if (createdNew)
                {
                    // 这是第一个实例
                    Application.Run(new Form1());
                    mutex.ReleaseMutex();
                }
                else
                {
                    // 已经有一个实例在运行
                    ActivateOtherWindow();
                    return;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用程序发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 释放互斥体
                mutex?.Dispose();
            }

        }

        private static void ActivateOtherWindow()
        {
            try
            {
                // 发送广播消息通知现有实例显示窗口
                PostMessage(new IntPtr(HWND_BROADCAST), (uint)showWindowMessage, IntPtr.Zero, IntPtr.Zero);

                // 等待一小段时间让现有实例处理消息
                System.Threading.Thread.Sleep(500);
            }
            catch (Exception ex)
            {
                // 记录错误但不显示消息框，避免干扰用户
                Debug.WriteLine($"发送显示窗口消息时发生错误: {ex.Message}");
            }
        }

    }
}