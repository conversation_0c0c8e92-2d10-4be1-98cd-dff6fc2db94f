﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;

namespace FlowCheck_DPLNG
{
    public class DataAnalyzer
    {
        private readonly string connectionString;
        private Dictionary<string, double> stabilityLimits; // 存储每个字段的稳定性限值

        public DataAnalyzer(string connectionString)
        {
            this.connectionString = connectionString;
        }

        // 初始化特定设备的稳定性限值
        private void InitializeStabilityLimits(string deviceId)
        {
            stabilityLimits = new Dictionary<string, double>
            {
                { $"{deviceId}_SLCT_METHANE", 0.1 },
                { $"{deviceId}_SLCT_ETHANE", 0.1 },
                { $"{deviceId}_SLCT_PROPANE", 0.1 },
                { $"{deviceId}_SLCT_N_BUTANE", 0.1 },
                { $"{deviceId}_SLCT_I_BUTANE", 0.1 },
                { $"{deviceId}_SLCT_N_PENTANE", 0.1 },
                { $"{deviceId}_SLCT_I_PENTANE", 0.1 },
                { $"{deviceId}_SLCT_NEO_PENTANE", 0.1 },
                { $"{deviceId}_SLCT_HEXANE", 0.1 },
                { $"{deviceId}_SLCT_HELIUM", 0.1 },
                { $"{deviceId}_SLCT_H2O", 0.1 },
                { $"{deviceId}_SLCT_H2S", 0.1 },
                { $"{deviceId}_SLCT_NITROGEN", 0.1 },
                { $"{deviceId}_SLCT_C6PLUS", 0.1 },
                { $"{deviceId}_SLCT_CO2", 0.1 },
                { $"{deviceId}_SLCT_HYDROGEN", 0.1 },
                { $"{deviceId}_TempInuse", 0.1 },
                { $"{deviceId}_PressInuse", 0.5 },
            };
        }

        public (bool isStable, List<double> middleValues) AnalyzeData(
            string deviceId,
            DateTime startTime,
            DateTime endTime
        )
        {
            // 初始化该设备的稳定性限值
            InitializeStabilityLimits(deviceId);

            // 1. 获取数据
            var fieldData = GetFieldData(deviceId, startTime, endTime);
            if (!fieldData.Any())
                return (false, null);

            // 2. 检查稳定性
            foreach (var field in fieldData)
            {
                double stdDev = CalculateStandardDeviation(field.Value);
                if (stabilityLimits.TryGetValue(field.Key, out double limit))
                {
                    if (stdDev > limit)
                        return (false, null);
                }
            }

            // 3. 获取中间位置的数据
            int middleIndex = fieldData.First().Value.Count / 2;
            var middleValues = fieldData.Select(field => field.Value[middleIndex]).ToList();

            return (true, middleValues);
        }

        private DateTime GetTimeAtIndex(
            string deviceId,
            DateTime startTime,
            DateTime endTime,
            int index
        )
        {
            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                string query =
                    $@"SELECT LocalTimeCol
                         FROM {deviceId}_FlowC
                         WHERE LocalTimeCol BETWEEN @StartTime AND @EndTime
                         ORDER BY LocalTimeCol
                         OFFSET {index} ROWS FETCH NEXT 1 ROWS ONLY";

                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@StartTime", startTime);
                    command.Parameters.AddWithValue("@EndTime", endTime);

                    return (DateTime)command.ExecuteScalar();
                }
            }
        }

        private Dictionary<string, List<double>> GetFieldData(
            string deviceId,
            DateTime startTime,
            DateTime endTime
        )
        {
            var result = new Dictionary<string, List<double>>();

            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                string query =
                    $@"SELECT * FROM {deviceId}_FlowC WHERE LocalTimeCol BETWEEN @StartTime AND @EndTime ORDER BY LocalTimeCol";

                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@StartTime", startTime);
                    command.Parameters.AddWithValue("@EndTime", endTime);

                    using (var reader = command.ExecuteReader())
                    {
                        // 获取所有带设备位号的列
                        var columns = Enumerable
                            .Range(0, reader.FieldCount)
                            .Select(i => reader.GetName(i))
                            .Where(name => name.StartsWith(deviceId))
                            .ToList();

                        // 初始化结果字典
                        foreach (var column in columns)
                        {
                            result[column] = new List<double>();
                        }

                        // 读取数据
                        while (reader.Read())
                        {
                            foreach (var column in columns)
                            {
                                if (!reader.IsDBNull(reader.GetOrdinal(column)))
                                {
                                    result[column].Add(reader.GetFloat(reader.GetOrdinal(column)));
                                }
                            }
                        }
                    }
                }
            }

            return result;
        }

        private double CalculateStandardDeviation(List<double> values)
        {
            if (values == null || values.Count == 0)
                return double.MaxValue;

            double average = values.Average();
            double sumOfSquaresOfDifferences = values.Sum(val => (val - average) * (val - average));
            double standardDeviation = Math.Sqrt(sumOfSquaresOfDifferences / values.Count);

            return standardDeviation;
        }

        private List<double> GetMiddleTimeValues(Dictionary<string, List<double>> fieldData)
        {
            var middleValues = new List<double>();
            int middleIndex = fieldData.First().Value.Count / 2;

            foreach (var field in fieldData)
            {
                if (field.Value.Count > middleIndex)
                {
                    middleValues.Add(field.Value[middleIndex]);
                }
            }

            return middleValues;
        }
    }
}
