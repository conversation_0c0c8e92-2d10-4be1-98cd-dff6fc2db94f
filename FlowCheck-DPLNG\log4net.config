﻿<?xml version="1.0" encoding="utf-8" ?>
<log4net>
	<appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
		<file type="log4net.Util.PatternString" value="logs\" />
		<appendToFile value="true" />
		<rollingStyle value="Date" />
		<datePattern value="yyyy-MM-dd'.log'" />
		<staticLogFileName value="false" />
		<preserveLogFileNameExtension value="false" />
		<!--<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date [%thread] %-5level %logger - %message (%file{1}:%line)%newline" />
		</layout>-->
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
		</layout>
	</appender>

	<root>
		<level value="ALL" />
		<appender-ref ref="RollingFileAppender" />
	</root>
</log4net>